import 'package:flutter_test/flutter_test.dart';
import 'package:intp_iraq/models/activation_code.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

void main() {
  group('ActivationCode Tests', () {
    test('should create ActivationCode with correct properties', () {
      final now = DateTime.now();
      final activationCode = ActivationCode(
        id: 'test-id',
        code: 'TEST1234',
        duration: SubscriptionDuration.oneMonth,
        createdAt: now,
        createdByAdminId: 'admin-id',
      );

      expect(activationCode.id, 'test-id');
      expect(activationCode.code, 'TEST1234');
      expect(activationCode.duration, SubscriptionDuration.oneMonth);
      expect(activationCode.isUsed, false);
      expect(activationCode.usedByUserId, null);
      expect(activationCode.usedAt, null);
      expect(activationCode.createdAt, now);
      expect(activationCode.createdByAdminId, 'admin-id');
    });

    test('should convert ActivationCode to Map correctly', () {
      final now = DateTime.now();
      final activationCode = ActivationCode(
        id: 'test-id',
        code: 'TEST1234',
        duration: SubscriptionDuration.threeMonths,
        createdAt: now,
        createdByAdminId: 'admin-id',
      );

      final map = activationCode.toMap();

      expect(map['code'], 'TEST1234');
      expect(map['duration'], 'three_months');
      expect(map['isUsed'], false);
      expect(map['usedByUserId'], null);
      expect(map['usedAt'], null);
      expect(map['createdAt'], isA<Timestamp>());
      expect(map['createdByAdminId'], 'admin-id');
    });

    test('should create ActivationCode from Map correctly', () {
      final now = DateTime.now();
      final map = {
        'code': 'TEST5678',
        'duration': 'six_months',
        'isUsed': true,
        'usedByUserId': 'user-123',
        'usedAt': Timestamp.fromDate(now),
        'createdAt': Timestamp.fromDate(now),
        'createdByAdminId': 'admin-456',
      };

      final activationCode = ActivationCode.fromMap(map, 'doc-id');

      expect(activationCode.id, 'doc-id');
      expect(activationCode.code, 'TEST5678');
      expect(activationCode.duration, SubscriptionDuration.sixMonths);
      expect(activationCode.isUsed, true);
      expect(activationCode.usedByUserId, 'user-123');
      expect(activationCode.usedAt, now);
      expect(activationCode.createdAt, now);
      expect(activationCode.createdByAdminId, 'admin-456');
    });

    test('should create copy with updated properties', () {
      final now = DateTime.now();
      final original = ActivationCode(
        id: 'test-id',
        code: 'TEST1234',
        duration: SubscriptionDuration.oneMonth,
        createdAt: now,
        createdByAdminId: 'admin-id',
      );

      final updated = original.copyWith(
        isUsed: true,
        usedByUserId: 'user-123',
        usedAt: now,
      );

      expect(updated.id, 'test-id');
      expect(updated.code, 'TEST1234');
      expect(updated.duration, SubscriptionDuration.oneMonth);
      expect(updated.isUsed, true);
      expect(updated.usedByUserId, 'user-123');
      expect(updated.usedAt, now);
      expect(updated.createdAt, now);
      expect(updated.createdByAdminId, 'admin-id');
    });
  });

  group('SubscriptionDuration Tests', () {
    test('should return correct display names', () {
      expect(SubscriptionDuration.oneMonth.displayName, 'شهر واحد');
      expect(SubscriptionDuration.threeMonths.displayName, '3 أشهر');
      expect(SubscriptionDuration.sixMonths.displayName, '6 أشهر');
      expect(SubscriptionDuration.oneYear.displayName, 'سنة واحدة');
    });

    test('should return correct month values', () {
      expect(SubscriptionDuration.oneMonth.months, 1);
      expect(SubscriptionDuration.threeMonths.months, 3);
      expect(SubscriptionDuration.sixMonths.months, 6);
      expect(SubscriptionDuration.oneYear.months, 12);
    });

    test('should return correct string values', () {
      expect(SubscriptionDuration.oneMonth.value, 'one_month');
      expect(SubscriptionDuration.threeMonths.value, 'three_months');
      expect(SubscriptionDuration.sixMonths.value, 'six_months');
      expect(SubscriptionDuration.oneYear.value, 'one_year');
    });

    test('should convert from string correctly', () {
      expect(SubscriptionDurationExtension.fromString('one_month'), SubscriptionDuration.oneMonth);
      expect(SubscriptionDurationExtension.fromString('three_months'), SubscriptionDuration.threeMonths);
      expect(SubscriptionDurationExtension.fromString('six_months'), SubscriptionDuration.sixMonths);
      expect(SubscriptionDurationExtension.fromString('one_year'), SubscriptionDuration.oneYear);
      expect(SubscriptionDurationExtension.fromString('invalid'), SubscriptionDuration.oneMonth);
    });
  });
}

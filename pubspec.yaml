name: intp_iraq
description: "This app provides a simulated environment for practicing the National Unified Exam in English, Arabic, and Computer Science. It helps users prepare by offering relevant questions, but it is not an official test."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.20+30

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  firebase_core: ^3.10.1 
  firebase_auth: ^5.4.1 
  path_provider: ^2.1.5
  cloud_firestore: ^5.6.2
  shared_preferences: ^2.5.1
  url_launcher: ^6.3.1
  flutter_inappwebview: ^6.1.5
  firebase_storage: ^12.4.4
  flutter_pdfview: ^1.4.0
  http: ^1.3.0
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  google_sign_in: ^6.2.2
  sign_in_with_apple: ^6.1.4
  crypto: ^3.0.6
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
# في نهاية الملف، أضف هذه الأسطر:
flutter:
  uses-material-design: true
  assets:
    - assets/images/login.png
    - assets/images/logo.png
    - assets/icon/icon.png
    - assets/images/gmail_logo.png
    - assets/images/google_logo.png
    - assets/images/apple_logo.png
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

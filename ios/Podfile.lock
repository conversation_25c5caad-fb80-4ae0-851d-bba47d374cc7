PODS:
  - abseil/algorithm (1.20240116.2):
    - abseil/algorithm/algorithm (= 1.20240116.2)
    - abseil/algorithm/container (= 1.20240116.2)
  - abseil/algorithm/algorithm (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.20240116.2):
    - abseil/base/atomic_hook (= 1.20240116.2)
    - abseil/base/base (= 1.20240116.2)
    - abseil/base/base_internal (= 1.20240116.2)
    - abseil/base/config (= 1.20240116.2)
    - abseil/base/core_headers (= 1.20240116.2)
    - abseil/base/cycleclock_internal (= 1.20240116.2)
    - abseil/base/dynamic_annotations (= 1.20240116.2)
    - abseil/base/endian (= 1.20240116.2)
    - abseil/base/errno_saver (= 1.20240116.2)
    - abseil/base/fast_type_id (= 1.20240116.2)
    - abseil/base/log_severity (= 1.20240116.2)
    - abseil/base/malloc_internal (= 1.20240116.2)
    - abseil/base/no_destructor (= 1.20240116.2)
    - abseil/base/nullability (= 1.20240116.2)
    - abseil/base/prefetch (= 1.20240116.2)
    - abseil/base/pretty_function (= 1.20240116.2)
    - abseil/base/raw_logging_internal (= 1.20240116.2)
    - abseil/base/spinlock_wait (= 1.20240116.2)
    - abseil/base/strerror (= 1.20240116.2)
    - abseil/base/throw_delegate (= 1.20240116.2)
  - abseil/base/atomic_hook (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/nullability (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/prefetch (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.20240116.2):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.20240116.2):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.20240116.2):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.20240116.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.20240116.2):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.20240116.2):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.20240116.2):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/debugging/examine_stack (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.20240116.2):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.20240116.2):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.20240116.2):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/log/absl_check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/xcprivacy
  - abseil/log/absl_log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/xcprivacy
  - abseil/log/absl_vlog_is_on (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/check (1.20240116.2):
    - abseil/log/internal/check_impl
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/globals (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/hash/hash
    - abseil/log/internal/vlog_config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/append_truncated (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/check_impl (1.20240116.2):
    - abseil/base/core_headers
    - abseil/log/internal/check_op
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/check_op (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/nullguard
    - abseil/log/internal/nullstream
    - abseil/log/internal/strip
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/conditions (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/log/internal/voidify
    - abseil/xcprivacy
  - abseil/log/internal/config (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/fnmatch (1.20240116.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/append_truncated
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/globals (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/strings/strings
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/log/internal/log_impl (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/log/internal/conditions
    - abseil/log/internal/log_message
    - abseil/log/internal/strip
    - abseil/xcprivacy
  - abseil/log/internal/log_message (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/examine_stack
    - abseil/log/globals
    - abseil/log/internal/append_truncated
    - abseil/log/internal/format
    - abseil/log/internal/globals
    - abseil/log/internal/log_sink_set
    - abseil/log/internal/nullguard
    - abseil/log/internal/proto
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/log/log_sink_registry
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/log_sink_set (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/base/no_destructor
    - abseil/base/raw_logging_internal
    - abseil/cleanup/cleanup
    - abseil/log/globals
    - abseil/log/internal/config
    - abseil/log/internal/globals
    - abseil/log/log_entry
    - abseil/log/log_sink
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/nullguard (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/log/internal/nullstream (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/log/internal/proto (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/internal/strip (1.20240116.2):
    - abseil/base/log_severity
    - abseil/log/internal/log_message
    - abseil/log/internal/nullstream
    - abseil/xcprivacy
  - abseil/log/internal/vlog_config (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/log/internal/fnmatch
    - abseil/memory/memory
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/log/internal/voidify (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/log/log (1.20240116.2):
    - abseil/log/internal/log_impl
    - abseil/log/vlog_is_on
    - abseil/xcprivacy
  - abseil/log/log_entry (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/log/internal/config
    - abseil/strings/strings
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/log/log_sink (1.20240116.2):
    - abseil/base/config
    - abseil/log/log_entry
    - abseil/xcprivacy
  - abseil/log/log_sink_registry (1.20240116.2):
    - abseil/base/config
    - abseil/log/internal/log_sink_set
    - abseil/log/log_sink
    - abseil/xcprivacy
  - abseil/log/vlog_is_on (1.20240116.2):
    - abseil/log/absl_vlog_is_on
    - abseil/xcprivacy
  - abseil/memory (1.20240116.2):
    - abseil/memory/memory (= 1.20240116.2)
  - abseil/memory/memory (1.20240116.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.20240116.2):
    - abseil/meta/type_traits (= 1.20240116.2)
  - abseil/meta/type_traits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/numeric/representation (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.20240116.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.20240116.2):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.20240116.2):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.20240116.2):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.20240116.2):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.20240116.2):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.20240116.2):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.20240116.2):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.20240116.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.20240116.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.20240116.2):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.20240116.2):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.20240116.2):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.20240116.2):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.20240116.2):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.20240116.2):
    - abseil/time/internal (= 1.20240116.2)
    - abseil/time/time (= 1.20240116.2)
  - abseil/time/internal (1.20240116.2):
    - abseil/time/internal/cctz (= 1.20240116.2)
  - abseil/time/internal/cctz (1.20240116.2):
    - abseil/time/internal/cctz/civil_time (= 1.20240116.2)
    - abseil/time/internal/cctz/time_zone (= 1.20240116.2)
  - abseil/time/internal/cctz/civil_time (1.20240116.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.20240116.2):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.20240116.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.20240116.2):
    - abseil/types/any (= 1.20240116.2)
    - abseil/types/bad_any_cast (= 1.20240116.2)
    - abseil/types/bad_any_cast_impl (= 1.20240116.2)
    - abseil/types/bad_optional_access (= 1.20240116.2)
    - abseil/types/bad_variant_access (= 1.20240116.2)
    - abseil/types/compare (= 1.20240116.2)
    - abseil/types/optional (= 1.20240116.2)
    - abseil/types/span (= 1.20240116.2)
    - abseil/types/variant (= 1.20240116.2)
  - abseil/types/any (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.20240116.2):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.20240116.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.20240116.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.20240116.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.20240116.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.20240116.2)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - BoringSSL-GRPC (0.0.36):
    - BoringSSL-GRPC/Implementation (= 0.0.36)
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Implementation (0.0.36):
    - BoringSSL-GRPC/Interface (= 0.0.36)
  - BoringSSL-GRPC/Interface (0.0.36)
  - cloud_firestore (5.6.3):
    - Firebase/Firestore (= 11.8.0)
    - firebase_core
    - Flutter
  - Firebase/Auth (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Firestore (11.8.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.8.0)
  - Firebase/Storage (11.8.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.8.0)
  - firebase_auth (5.4.2):
    - Firebase/Auth (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.1):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_storage (12.4.4):
    - Firebase/Storage (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (11.9.0)
  - FirebaseAuth (11.8.1):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.9.0)
  - FirebaseCore (11.8.1):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseFirestore (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - FirebaseFirestoreInternal (= 11.8.0)
    - FirebaseSharedSwift (~> 11.0)
  - FirebaseFirestoreInternal (11.8.0):
    - abseil/algorithm (~> 1.20240116.1)
    - abseil/base (~> 1.20240116.1)
    - abseil/container/flat_hash_map (~> 1.20240116.1)
    - abseil/memory (~> 1.20240116.1)
    - abseil/meta (~> 1.20240116.1)
    - abseil/strings/strings (~> 1.20240116.1)
    - abseil/time (~> 1.20240116.1)
    - abseil/types (~> 1.20240116.1)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - "gRPC-C++ (~> 1.65.0)"
    - gRPC-Core (~> 1.65.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseSharedSwift (11.9.0)
  - FirebaseStorage (11.8.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_pdfview (1.0.2):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.65.5)":
    - "gRPC-C++/Implementation (= 1.65.5)"
    - "gRPC-C++/Interface (= 1.65.5)"
  - "gRPC-C++/Implementation (1.65.5)":
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/absl_check (~> 1.20240116.2)
    - abseil/log/absl_log (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - "gRPC-C++/Interface (= 1.65.5)"
    - "gRPC-C++/Privacy (= 1.65.5)"
    - gRPC-Core (= 1.65.5)
  - "gRPC-C++/Interface (1.65.5)"
  - "gRPC-C++/Privacy (1.65.5)"
  - gRPC-Core (1.65.5):
    - gRPC-Core/Implementation (= 1.65.5)
    - gRPC-Core/Interface (= 1.65.5)
  - gRPC-Core/Implementation (1.65.5):
    - abseil/algorithm/container (~> 1.20240116.2)
    - abseil/base/base (~> 1.20240116.2)
    - abseil/base/config (~> 1.20240116.2)
    - abseil/base/core_headers (~> 1.20240116.2)
    - abseil/base/log_severity (~> 1.20240116.2)
    - abseil/base/no_destructor (~> 1.20240116.2)
    - abseil/cleanup/cleanup (~> 1.20240116.2)
    - abseil/container/flat_hash_map (~> 1.20240116.2)
    - abseil/container/flat_hash_set (~> 1.20240116.2)
    - abseil/container/inlined_vector (~> 1.20240116.2)
    - abseil/flags/flag (~> 1.20240116.2)
    - abseil/flags/marshalling (~> 1.20240116.2)
    - abseil/functional/any_invocable (~> 1.20240116.2)
    - abseil/functional/bind_front (~> 1.20240116.2)
    - abseil/functional/function_ref (~> 1.20240116.2)
    - abseil/hash/hash (~> 1.20240116.2)
    - abseil/log/check (~> 1.20240116.2)
    - abseil/log/globals (~> 1.20240116.2)
    - abseil/log/log (~> 1.20240116.2)
    - abseil/memory/memory (~> 1.20240116.2)
    - abseil/meta/type_traits (~> 1.20240116.2)
    - abseil/random/bit_gen_ref (~> 1.20240116.2)
    - abseil/random/distributions (~> 1.20240116.2)
    - abseil/random/random (~> 1.20240116.2)
    - abseil/status/status (~> 1.20240116.2)
    - abseil/status/statusor (~> 1.20240116.2)
    - abseil/strings/cord (~> 1.20240116.2)
    - abseil/strings/str_format (~> 1.20240116.2)
    - abseil/strings/strings (~> 1.20240116.2)
    - abseil/synchronization/synchronization (~> 1.20240116.2)
    - abseil/time/time (~> 1.20240116.2)
    - abseil/types/optional (~> 1.20240116.2)
    - abseil/types/span (~> 1.20240116.2)
    - abseil/types/variant (~> 1.20240116.2)
    - abseil/utility/utility (~> 1.20240116.2)
    - BoringSSL-GRPC (= 0.0.36)
    - gRPC-Core/Interface (= 1.65.5)
    - gRPC-Core/Privacy (= 1.65.5)
  - gRPC-Core/Interface (1.65.5)
  - gRPC-Core/Privacy (1.65.5)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (6.0.3)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - RecaptchaInterop (100.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleSignIn
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - OrderedSet
    - RecaptchaInterop

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  abseil: d121da9ef7e2ff4cab7666e76c5a3e0915ae08c3
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  BoringSSL-GRPC: ca6a8e5d04812fce8ffd6437810c2d46f925eaeb
  cloud_firestore: c86f040eed68db94fae601cc3311085e24f6c15d
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_auth: 342560b5af9939733fa3f9d5be7ee7e4456c9e71
  firebase_core: 8d552814f6c01ccde5d88939fced4ec26f2f5510
  firebase_storage: 4ed3dade149f74651bdae3536b68d403c364591c
  FirebaseAppCheckInterop: 9226f7217b43e99dfa0bc9f674ad8108cef89feb
  FirebaseAuth: ad59a1a7b161e75f74c39f70179d2482d40e2737
  FirebaseAuthInterop: 2a26ee1bea6d47df8048683cfa071e7da657798f
  FirebaseCore: 99fe0c4b44a39f37d99e6404e02009d2db5d718d
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseFirestore: 563a4ab1a65e2858f05e150bb4c31b0f8f79248b
  FirebaseFirestoreInternal: 8c5921c360a70e447bfeefb245f450e8b50e750b
  FirebaseSharedSwift: 574e6a5602afe4397a55c8d4f767382d620285de
  FirebaseStorage: 8eede00081a6ce904eaa8d2daa66f1e053e8e6ea
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  google_sign_in_ios: 0ab078e60da6dfe23cbc55c83502b52bba1aad63
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  "gRPC-C++": 2fa52b3141e7789a28a737f251e0c45b4cb20a87
  gRPC-Core: a27c294d6149e1c39a7d173527119cfbc3375ce4
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.16.2

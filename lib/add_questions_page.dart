import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AddQuestionsPage extends StatefulWidget {
  const AddQuestionsPage({super.key});

  @override
  _AddQuestionsPageState createState() => _AddQuestionsPageState();
}

class _AddQuestionsPageState extends State<AddQuestionsPage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final _formKey = GlobalKey<FormState>();
  final _bulkFormKey = GlobalKey<FormState>();

  String _question = '';
  List<String> _answers = ['', '', '', ''];
  String _correctAnswer = '';
  String _selectedCollection = 'arabic_questions';
  String _bulkQuestions = '';
  bool _isBulkMode = false;

  final List<String> _collections = [
    'arabic_questions',
    'computer_questions',
    'english_questions'
  ];

  // الحصول على رقم الوثيقة التالي
  Future<int> _getNextDocumentNumber() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection(_selectedCollection).get();

      if (snapshot.docs.isEmpty) {
        return 1;
      }

      int maxNumber = 0;
      for (var doc in snapshot.docs) {
        int currentNumber = int.tryParse(doc.id.replaceAll('document', '')) ?? 0;
        if (currentNumber > maxNumber) {
          maxNumber = currentNumber;
        }
      }

      return maxNumber + 1;
    } catch (e) {
      print("Error fetching documents: $e");
      return 1; // Return 1 as a default if there's an error
    }
  }

  // إضافة السؤال إلى Firestore
  Future<void> _addQuestion() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      try {
        int nextNumber = await _getNextDocumentNumber();
        String docId = 'document$nextNumber';

        await _firestore.collection(_selectedCollection).doc(docId).set({
          "question": _question,
          "answers": _answers,
          "correctAnswer": int.parse(_correctAnswer) - 1 // Convert to index (0-3)
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تمت إضافة السؤال بنجاح (رقم $nextNumber) إلى $_selectedCollection')),
        );
        _resetForm();
      } catch (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء إضافة السؤال: $error')),
        );
      }
    }
  }

  // إضافة مجموعة من الأسئلة إلى Firestore
  Future<void> _addBulkQuestions() async {
    if (_bulkFormKey.currentState!.validate()) {
      _bulkFormKey.currentState!.save();
      try {
        List<String> lines = _bulkQuestions.split('\n')
            .where((line) => line.trim().isNotEmpty)
            .toList();

        int successCount = 0;
        int nextNumber = await _getNextDocumentNumber();

        for (String line in lines) {
          List<String> parts = line.split('|').map((e) => e.trim()).toList();
          if (parts.length >= 6) { // سؤال + 4 إجابات + رقم الإجابة الصحيحة
            String question = parts[0];
            List<String> answers = parts.sublist(1, 5);
            int correctAnswer = int.tryParse(parts[5])! - 1;

            String docId = 'document${nextNumber + successCount}';
            await _firestore.collection(_selectedCollection).doc(docId).set({
              "question": question,
              "answers": answers,
              "correctAnswer": correctAnswer
            });
            successCount++;
          }
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تمت إضافة $successCount سؤال بنجاح')),
        );
        _bulkQuestions = '';
        _bulkFormKey.currentState!.reset();
      } catch (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء إضافة الأسئلة: $error')),
        );
      }
    }
  }

  // إعادة تعيين النموذج
  void _resetForm() {
    setState(() {
      _question = '';
      _answers = ['', '', '', ''];
      _correctAnswer = '';
    });
    _formKey.currentState!.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إضافة سؤال جديد'),
        leading: IconButton(
          icon: Icon(CupertinoIcons.back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(_isBulkMode ? Icons.article : Icons.article_outlined),
            onPressed: () {
              setState(() {
                _isBulkMode = !_isBulkMode;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _selectedCollection,
              icon: Icon(CupertinoIcons.chevron_down),
              decoration: InputDecoration(
                labelText: 'اختر المجموعة',
                border: OutlineInputBorder(),
              ),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedCollection = newValue!;
                });
              },
              items: _collections.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
            SizedBox(height: 20),
            if (!_isBulkMode) Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    decoration: InputDecoration(
                      labelText: 'السؤال',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value!.isEmpty ? 'الرجاء إدخال السؤال' : null,
                    onSaved: (value) => _question = value!,
                  ),
                  SizedBox(height: 20),
                  ...List.generate(4, (index) => 
                    Padding(
                      padding: EdgeInsets.only(bottom: 10),
                      child: TextFormField(
                        decoration: InputDecoration(
                          labelText: 'الإجابة ${index + 1}',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => value!.isEmpty ? 'الرجاء إدخال الإجابة' : null,
                        onSaved: (value) => _answers[index] = value!,
                      ),
                    )
                  ),
                  SizedBox(height: 20),
                  TextFormField(
                    decoration: InputDecoration(
                      labelText: 'رقم الإجابة الصحيحة (1-4)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value!.isEmpty) return 'الرجاء إدخال رقم الإجابة الصحيحة';
                      int? number = int.tryParse(value);
                      if (number == null || number < 1 || number > 4) {
                        return 'الرجاء إدخال رقم بين 1 و 4';
                      }
                      return null;
                    },
                    onSaved: (value) => _correctAnswer = value!,
                  ),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _addQuestion,
                    child: Text('إضافة السؤال'),
                  ),
                ],
              ),
            ),
            if (_isBulkMode) Form(
              key: _bulkFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تعليمات إضافة الأسئلة:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '• كل سطر يمثل سؤالاً واحداً\n'
                            '• استخدم | للفصل بين العناصر\n'
                            '• الترتيب: السؤال | إجابة1 | إجابة2 | إجابة3 | إجابة4 | رقم الإجابة الصحيحة\n'
                            '• مثال:\n'
                            'ما هي عاصمة العراق؟|بغداد|البصرة|أربيل|الموصل|1'
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 20),
                  TextFormField(
                    decoration: InputDecoration(
                      labelText: 'أدخل الأسئلة',
                      border: OutlineInputBorder(),
                      alignLabelWithHint: true,
                    ),
                    maxLines: 10,
                    validator: (value) => value!.isEmpty ? 'الرجاء إدخال الأسئلة' : null,
                    onSaved: (value) => _bulkQuestions = value!,
                  ),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _addBulkQuestions,
                    child: Text('إضافة جميع الأسئلة'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
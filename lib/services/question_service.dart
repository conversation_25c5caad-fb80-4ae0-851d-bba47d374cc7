import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intp_iraq/services/cache_service.dart';

class QuestionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // تحديث اسم المجموعة حسب نوع الامتحان
  static String _getCollectionName(String examType) {
    return '${examType}_questions';
  }

  static Future<List<Map<String, dynamic>>> getQuestions(String examType) async {
    try {
      print('Getting questions for: $examType');

      // محاولة جلب الأسئلة من الكاش أولاً
      final cachedQuestions = await CacheService.getCachedQuestions(examType);
      if (cachedQuestions != null && cachedQuestions.isNotEmpty) {
        print('Found ${cachedQuestions.length} cached questions');
        return cachedQuestions;
      }

      // جلب الأسئلة من Firestore من المجموعة المناسبة
      final QuerySnapshot questionSnapshot = await _firestore
          .collection(_getCollectionName(examType))
          .get();

      print('Firestore returned ${questionSnapshot.docs.length} documents');

      if (questionSnapshot.docs.isEmpty) {
        throw Exception('No questions found for $examType');
      }

      List<Map<String, dynamic>> questions = questionSnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'question': data['question'] ?? '',
          'answers': List<String>.from(data['answers'] ?? []),
          'correctAnswer': data['correctAnswer'] ?? 0,
        };
      }).toList();

      // حفظ في الكاش
      if (questions.isNotEmpty) {
        await CacheService.cacheQuestions(examType, questions);
        print('Questions cached successfully');
      }

      return questions;
    } catch (e, stackTrace) {
      print('Error in getQuestions: $e');
      print('Stack trace: $stackTrace');
      throw e;
    }
  }

  // الحصول على الأسئلة الجديدة فقط من Firestore
  static Future<List<Map<String, dynamic>>> getNewQuestions(String examType) async {
    try {
      print('Getting new questions for: $examType');
      
      // الحصول على معرف آخر سؤال تم تخزينه في الكاش
      final lastQuestionId = await CacheService.getLastQuestionId(examType);
      print('Last question ID for $examType: ${lastQuestionId ?? "None"}');
      
      Query query = _firestore.collection(_getCollectionName(examType));
      
      // إذا كان هناك معرف سؤال سابق، قم بجلب الأسئلة الجديدة فقط (رقمها أكبر من الأخير)
      if (lastQuestionId != null) {
        // افتراض أن المعرف يبدأ بكلمة "document" متبوعًا برقم
        if (lastQuestionId.startsWith("document")) {
          final lastNumber = int.tryParse(lastQuestionId.substring(8)) ?? 0;
          // استعلام للحصول على المستندات التي تبدأ باسم أكبر من آخر معرف
          query = query.where(FieldPath.documentId, isGreaterThan: 'document$lastNumber');
        } else {
          // في حالة كان المعرف مختلف عن النمط المتوقع
          query = query.where(FieldPath.documentId, isGreaterThan: lastQuestionId);
        }
      }
      
      final QuerySnapshot questionSnapshot = await query.get();
      print('Firestore returned ${questionSnapshot.docs.length} new documents');
      
      if (questionSnapshot.docs.isEmpty) {
        return [];
      }

      List<Map<String, dynamic>> newQuestions = questionSnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'question': data['question'] ?? '',
          'answers': List<String>.from(data['answers'] ?? []),
          'correctAnswer': data['correctAnswer'] ?? 0,
        };
      }).toList();
      
      return newQuestions;
    } catch (e, stackTrace) {
      print('Error in getNewQuestions: $e');
      print('Stack trace: $stackTrace');
      throw e;
    }
  }

  // تحديث الأسئلة بجلب الأسئلة الجديدة فقط
  static Future<void> updateQuestionsSmartly() async {
    try {
      print('Starting smart questions update');
      
      final examTypes = ['english', 'arabic', 'computer'];
      
      for (var examType in examTypes) {
        print('Smartly updating $examType questions');
        
        // جلب الأسئلة الجديدة فقط
        final newQuestions = await getNewQuestions(examType);
        
        if (newQuestions.isNotEmpty) {
          // إضافة الأسئلة الجديدة فقط للكاش
          await CacheService.addNewQuestions(examType, newQuestions);
          print('Updated cache for $examType with ${newQuestions.length} new questions');
        } else {
          print('No new questions found for $examType');
        }
      }
    } catch (e) {
      print('Error updating questions smartly: $e');
      throw e;
    }
  }

  // نحتفظ بالدالة القديمة للتوافقية الخلفية
  static Future<void> updateQuestions() async {
    try {
      print('Starting questions update (legacy method)');
      await CacheService.clearAllCache();
      
      final examTypes = ['english', 'arabic', 'computer'];
      
      for (var examType in examTypes) {
        print('Updating $examType questions');
        
        final QuerySnapshot questionSnapshot = await _firestore
            .collection(_getCollectionName(examType))
            .get();

        List<Map<String, dynamic>> questions = questionSnapshot.docs.map((doc) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          return {
            'id': doc.id,
            'question': data['question'] ?? '',
            'answers': List<String>.from(data['answers'] ?? []),
            'correctAnswer': data['correctAnswer'] ?? 0,
          };
        }).toList();

        if (questions.isNotEmpty) {
          await CacheService.cacheQuestions(examType, questions);
          // حفظ آخر معرف سؤال
          if (questions.isNotEmpty) {
            String lastId = questions.last['id'].toString();
            await CacheService.saveLastQuestionId(examType, lastId);
          }
          print('Updated cache for $examType with ${questions.length} questions');
        }
      }
    } catch (e) {
      print('Error updating questions: $e');
      throw e;
    }
  }
}
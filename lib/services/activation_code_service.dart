import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/activation_code.dart';

class ActivationCodeService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'activation_codes';

  // إنشاء كود تفعيل جديد
  static Future<ActivationCode?> generateActivationCode(SubscriptionDuration duration) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return null;

      // التحقق من أن المستخدم أدمن
      if (user.uid != 'VTmLpE1WBjPHZF7UXezhKXeSLA83') {
        throw Exception('غير مصرح لك بإنشاء أكواد التفعيل');
      }

      // إنشاء كود عشوائي
      String code = _generateRandomCode();
      
      // التأكد من أن الكود غير مستخدم
      while (await _isCodeExists(code)) {
        code = _generateRandomCode();
      }

      final activationCode = ActivationCode(
        id: '', // سيتم تعيينه من Firestore
        code: code,
        duration: duration,
        createdAt: DateTime.now(),
        createdByAdminId: user.uid,
      );

      // حفظ الكود في Firestore
      DocumentReference docRef = await _firestore
          .collection(_collectionName)
          .add(activationCode.toMap());

      return activationCode.copyWith(id: docRef.id);
    } catch (e) {
      print('Error generating activation code: $e');
      return null;
    }
  }

  // التحقق من صحة كود التفعيل وتفعيل الاشتراك
  static Future<bool> activateSubscription(String code) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      // البحث عن الكود
      QuerySnapshot codeQuery = await _firestore
          .collection(_collectionName)
          .where('code', isEqualTo: code)
          .where('isUsed', isEqualTo: false)
          .limit(1)
          .get();

      if (codeQuery.docs.isEmpty) {
        return false; // الكود غير موجود أو مستخدم
      }

      DocumentSnapshot codeDoc = codeQuery.docs.first;
      ActivationCode activationCode = ActivationCode.fromMap(
        codeDoc.data() as Map<String, dynamic>,
        codeDoc.id,
      );

      // حساب تاريخ انتهاء الاشتراك
      DateTime expiryDate = DateTime.now().add(Duration(days: activationCode.duration.months * 30));

      // تحديث بيانات المستخدم
      await _updateUserSubscription(user.uid, expiryDate);

      // تحديث حالة الكود
      await _firestore.collection(_collectionName).doc(codeDoc.id).update({
        'isUsed': true,
        'usedByUserId': user.uid,
        'usedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error activating subscription: $e');
      return false;
    }
  }

  // الحصول على جميع أكواد التفعيل (للأدمن فقط)
  static Future<List<ActivationCode>> getAllActivationCodes() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null || user.uid != 'VTmLpE1WBjPHZF7UXezhKXeSLA83') {
        return [];
      }

      QuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        return ActivationCode.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }).toList();
    } catch (e) {
      print('Error getting activation codes: $e');
      return [];
    }
  }

  // إنشاء كود عشوائي
  static String _generateRandomCode() {
    const String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    Random random = Random();
    return String.fromCharCodes(Iterable.generate(
      8, (_) => chars.codeUnitAt(random.nextInt(chars.length))
    ));
  }

  // التحقق من وجود الكود
  static Future<bool> _isCodeExists(String code) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection(_collectionName)
          .where('code', isEqualTo: code)
          .limit(1)
          .get();
      return snapshot.docs.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // تحديث اشتراك المستخدم
  static Future<void> _updateUserSubscription(String userId, DateTime expiryDate) async {
    try {
      // التحقق من طريقة المصادقة للمستخدم
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final isGoogleSignIn = user.providerData
          .any((element) => element.providerId == 'google.com');

      if (isGoogleSignIn) {
        // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
        final userDocs = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();
            
        if (userDocs.docs.isNotEmpty) {
          final docId = userDocs.docs.first.id;
          await _firestore.collection('users').doc(docId).update({
            'isPremium': true,
            'premiumStartDate': FieldValue.serverTimestamp(),
            'premiumExpiryDate': Timestamp.fromDate(expiryDate),
          });
        }
      } else {
        // للمستخدمين العاديين، نستخدم معرف المستخدم
        await _firestore.collection('users').doc(userId).update({
          'isPremium': true,
          'premiumStartDate': FieldValue.serverTimestamp(),
          'premiumExpiryDate': Timestamp.fromDate(expiryDate),
        });
      }
    } catch (e) {
      print('Error updating user subscription: $e');
      throw e;
    }
  }

  // حذف كود تفعيل
  static Future<bool> deleteActivationCode(String codeId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null || user.uid != 'VTmLpE1WBjPHZF7UXezhKXeSLA83') {
        throw Exception('غير مصرح لك بحذف أكواد التفعيل');
      }

      await _firestore.collection(_collectionName).doc(codeId).delete();
      return true;
    } catch (e) {
      print('Error deleting activation code: $e');
      return false;
    }
  }
}

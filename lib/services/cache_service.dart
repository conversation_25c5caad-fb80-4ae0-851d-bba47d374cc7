import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheService {
  static const String _questionsKey = 'cached_questions_';
  static const String _lastQuestionIdKey = 'last_question_id_';

  static Future<void> cacheQuestions(String examType, List<Map<String, dynamic>> questions) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String jsonData = jsonEncode(questions);
      await prefs.setString(_questionsKey + examType, jsonData);
      print('Cached ${questions.length} questions for $examType');
    } catch (e) {
      print('Error caching questions: $e');
      throw e;
    }
  }

  static Future<List<Map<String, dynamic>>?> getCachedQuestions(String examType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? jsonData = prefs.getString(_questionsKey + examType);
      
      if (jsonData == null || jsonData.isEmpty) {
        print('No cached data found for $examType');
        return null;
      }

      List<dynamic> decodedList = jsonDecode(jsonData);
      final questions = decodedList.map((item) => Map<String, dynamic>.from(item)).toList();
      print('Retrieved ${questions.length} questions from cache for $examType');
      return questions;
    } catch (e) {
      print('Error getting cached questions: $e');
      return null;
    }
  }

  static Future<void> clearCache(String examType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_questionsKey + examType);
      print('Cache cleared for $examType');
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  static Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final examTypes = ['english', 'arabic', 'computer'];
      for (var type in examTypes) {
        await prefs.remove(_questionsKey + type);
      }
      print('All caches cleared');
    } catch (e) {
      print('Error clearing all caches: $e');
    }
  }

  // حفظ معرف آخر سؤال تم تخزينه
  static Future<void> saveLastQuestionId(String examType, String lastId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastQuestionIdKey + examType, lastId);
      print('Saved last question ID for $examType: $lastId');
    } catch (e) {
      print('Error saving last question ID: $e');
    }
  }

  // الحصول على معرف آخر سؤال تم تخزينه
  static Future<String?> getLastQuestionId(String examType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastQuestionIdKey + examType);
    } catch (e) {
      print('Error getting last question ID: $e');
      return null;
    }
  }

  // إضافة أسئلة جديدة للكاش بدون حذف القديمة
  static Future<void> addNewQuestions(String examType, List<Map<String, dynamic>> newQuestions) async {
    if (newQuestions.isEmpty) {
      print('No new questions to add');
      return;
    }

    try {
      // الحصول على الأسئلة الحالية من الكاش
      final currentQuestions = await getCachedQuestions(examType) ?? [];
      
      // إضافة الأسئلة الجديدة
      final allQuestions = [...currentQuestions, ...newQuestions];
      
      // حفظ جميع الأسئلة في الكاش
      final prefs = await SharedPreferences.getInstance();
      final String jsonData = jsonEncode(allQuestions);
      await prefs.setString(_questionsKey + examType, jsonData);
      
      // حفظ معرف آخر سؤال
      if (newQuestions.isNotEmpty) {
        String lastId = newQuestions.last['id'].toString();
        await saveLastQuestionId(examType, lastId);
      }
      
      print('Added ${newQuestions.length} new questions for $examType, total: ${allQuestions.length}');
    } catch (e) {
      print('Error adding new questions to cache: $e');
      throw e;
    }
  }
}

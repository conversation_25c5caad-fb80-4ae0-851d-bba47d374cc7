import 'package:cloud_firestore/cloud_firestore.dart';

enum SubscriptionDuration {
  oneMonth,
  threeMonths,
  sixMonths,
  oneYear,
}

extension SubscriptionDurationExtension on SubscriptionDuration {
  String get displayName {
    switch (this) {
      case SubscriptionDuration.oneMonth:
        return 'شهر واحد';
      case SubscriptionDuration.threeMonths:
        return '3 أشهر';
      case SubscriptionDuration.sixMonths:
        return '6 أشهر';
      case SubscriptionDuration.oneYear:
        return 'سنة واحدة';
    }
  }

  int get months {
    switch (this) {
      case SubscriptionDuration.oneMonth:
        return 1;
      case SubscriptionDuration.threeMonths:
        return 3;
      case SubscriptionDuration.sixMonths:
        return 6;
      case SubscriptionDuration.oneYear:
        return 12;
    }
  }

  String get value {
    switch (this) {
      case SubscriptionDuration.oneMonth:
        return 'one_month';
      case SubscriptionDuration.threeMonths:
        return 'three_months';
      case SubscriptionDuration.sixMonths:
        return 'six_months';
      case SubscriptionDuration.oneYear:
        return 'one_year';
    }
  }

  static SubscriptionDuration fromString(String value) {
    switch (value) {
      case 'one_month':
        return SubscriptionDuration.oneMonth;
      case 'three_months':
        return SubscriptionDuration.threeMonths;
      case 'six_months':
        return SubscriptionDuration.sixMonths;
      case 'one_year':
        return SubscriptionDuration.oneYear;
      default:
        return SubscriptionDuration.oneMonth;
    }
  }
}

class ActivationCode {
  final String id;
  final String code;
  final SubscriptionDuration duration;
  final bool isUsed;
  final String? usedByUserId;
  final DateTime? usedAt;
  final DateTime createdAt;
  final String createdByAdminId;

  ActivationCode({
    required this.id,
    required this.code,
    required this.duration,
    this.isUsed = false,
    this.usedByUserId,
    this.usedAt,
    required this.createdAt,
    required this.createdByAdminId,
  });

  // تحويل من Map إلى ActivationCode
  factory ActivationCode.fromMap(Map<String, dynamic> map, String id) {
    return ActivationCode(
      id: id,
      code: map['code'] ?? '',
      duration: SubscriptionDurationExtension.fromString(map['duration'] ?? 'one_month'),
      isUsed: map['isUsed'] ?? false,
      usedByUserId: map['usedByUserId'],
      usedAt: map['usedAt'] != null ? (map['usedAt'] as Timestamp).toDate() : null,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  // تحويل من ActivationCode إلى Map
  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'duration': duration.value,
      'isUsed': isUsed,
      'usedByUserId': usedByUserId,
      'usedAt': usedAt != null ? Timestamp.fromDate(usedAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdByAdminId': createdByAdminId,
    };
  }

  // إنشاء نسخة محدثة من الكود
  ActivationCode copyWith({
    String? id,
    String? code,
    SubscriptionDuration? duration,
    bool? isUsed,
    String? usedByUserId,
    DateTime? usedAt,
    DateTime? createdAt,
    String? createdByAdminId,
  }) {
    return ActivationCode(
      id: id ?? this.id,
      code: code ?? this.code,
      duration: duration ?? this.duration,
      isUsed: isUsed ?? this.isUsed,
      usedByUserId: usedByUserId ?? this.usedByUserId,
      usedAt: usedAt ?? this.usedAt,
      createdAt: createdAt ?? this.createdAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }

  @override
  String toString() {
    return 'ActivationCode(id: $id, code: $code, duration: ${duration.displayName}, isUsed: $isUsed)';
  }
}

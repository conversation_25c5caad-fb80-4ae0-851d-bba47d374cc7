import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intp_iraq/screens/home_page.dart';

class GoogleSignInPage extends StatefulWidget {
  const GoogleSignInPage({super.key});

  @override
  _GoogleSignInPageState createState() => _GoogleSignInPageState();
}

class _GoogleSignInPageState extends State<GoogleSignInPage> {
  final _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _handleGoogleSignIn();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(fontFamily: 'Cairo')),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  Future<void> _handleGoogleSignIn() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      // بدء تسجيل الدخول باستخدام جوجل
      final GoogleSignIn googleSignIn = GoogleSignIn();
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        if (!mounted) return;
        Navigator.pop(context);
        return;
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // تسجيل الدخول في Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final UserCredential userCredential = 
          await FirebaseAuth.instance.signInWithCredential(credential);
      final User? user = userCredential.user;

      if (user != null) {
        // البحث عن المستخدم باستخدام البريد الإلكتروني بدلاً من UID
        final userDocQuery = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();

        Map<String, dynamic> userData = {
          'username': user.displayName ?? user.email?.split('@')[0] ?? 'مستخدم',
          'email': user.email,
          'phone': '', // إضافة حقل هاتف فارغ لتجنب الأخطاء
          'lastLogin': FieldValue.serverTimestamp(),
          'emailVerified': true,
        };

        // إذا كان المستخدم موجوداً، احتفظ بحالة الاشتراك المدفوع
        if (userDocQuery.docs.isNotEmpty) {
          DocumentSnapshot existingDoc = userDocQuery.docs.first;
          Map<String, dynamic> existingData = existingDoc.data() as Map<String, dynamic>;
          
          // استخدام معرّف المستند الحالي
          await _firestore.collection('users').doc(existingDoc.id).update({
            ...userData,
            // الاحتفاظ بحالة الاشتراك المدفوع إن وجدت
            'isPremium': existingData['isPremium'] ?? false,
            // نتأكد من وجود حقل الهاتف حتى لو كان فارغاً
            'phone': existingData['phone'] ?? '',
          });
        } else {
          // إنشاء مستخدم جديد باستخدام UID كمعرّف
          await _firestore.collection('users').doc(user.uid).set({
            ...userData,
            'isPremium': false,
            'examAttempts': {},
            'phone': '', // تأكيد إضافة حقل هاتف فارغ للمستخدمين الجدد
            'createdAt': FieldValue.serverTimestamp(),
          });
        }

        if (mounted) {
          _showSnackBar('تم تسجيل الدخول بنجاح');
          
          // تأخير قصير قبل الانتقال
          await Future.delayed(Duration(milliseconds: 500));
          
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => HomePage()),
            );
          }
        }
      }
    } catch (e) {
      print('خطأ في تسجيل الدخول بجوجل: $e');
      if (mounted) {
        _showSnackBar('حدث خطأ أثناء تسجيل الدخول، يرجى المحاولة مرة أخرى.', isError: true);
        Navigator.pop(context);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 20),
                  Text(
                    'جاري تسجيل الدخول باستخدام Google...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                    ),
                  ),
                ],
              )
            : Text('جاري التحميل...', style: TextStyle(fontFamily: 'Cairo')),
      ),
    );
  }
}
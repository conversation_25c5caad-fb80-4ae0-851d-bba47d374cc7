import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class TipsGuidancePage extends StatelessWidget {
  const TipsGuidancePage({super.key});

  void _showTipDetails(BuildContext context, String title, String content) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: CupertinoAlertDialog(
            title: Text(
              title,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              content,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 16,
              ),
            ),
            actions: [
              CupertinoDialogAction(
                child: Text(
                  'حسناً',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    color: Colors.blue,
                  ),
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTipButton(BuildContext context, String title, IconData icon, String content) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
        child: CupertinoButton(
          onPressed: () => _showTipDetails(context, title, content),
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(15),
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
          child: Row(
            children: [
              Icon(
                icon,
                color: Colors.blue.shade800,
                size: 30,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          actions: [
            Directionality(
              textDirection: TextDirection.ltr,
              child: IconButton(
                icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ],
          title: Text(
            'نصائح وإرشادات',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 20,
              color: Colors.blue.shade800,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: Container(
          color: Colors.white,
          child: SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildTipButton(
                      context,
                      'حجز وتأكيد الامتحان',
                      CupertinoIcons.calendar_badge_plus,
                      'يجب عليك اتمام حجز الامتحان عن طريق الرابط الرسمي واختيار المركز واليوم والوقت المناسب.\n'
                    ),
                    const SizedBox(height: 20),
                    _buildTipButton(
                      context,
                      'الاستعداد للامتحان',
                      CupertinoIcons.lightbulb_fill,
                      'قم بالإعداد جيداً للامتحان من خلال:\n\n'
                      '• استخدام المحاكي الخاص بنا للتدرب على نمط الأسئلة\n'
                      '• دراسة الملازم الخاصة بكل مادة\n'
                      '• حل نماذج الامتحانات السابقة\n'
                      '• تنظيم وقت المذاكرة بشكل فعال'
                    ),
                    const SizedBox(height: 20),
                    _buildTipButton(
                      context,
                      'أفضل طريقة للاستعداد لامتحان اللغة الإنجليزية',
                      CupertinoIcons.book_fill,
                      'أفضل طريقة لتحسين مستواك في امتحان للغة الإنجليزية هي من خلال حل الاختبارات التجريبية الخاصة بتطبيقنا. عند مراجعة إجاباتك، لا تكتفِ بمعرفة الأسئلة التي أخطأت بها، بل ابحث عن سبب الخطأ وفهم القاعدة الصحيحة. يمكنك الاستعانة بأدوات الذكاء الاصطناعي لتوضيح الأخطاء وتحليل الإجابات بشكل أعمق.\n\n'
                      'كما أن قراءة القطع الخارجية أمر مهم جدًا، وتطبيقنا يحتوي على مجموعة كبيرة من القطع الخارجية التي تساعدك على التدرب. لكن يجب أن تدرك أن القطعة في الامتحان ستكون مختلفة، ولهذا عليك أن تتعود على نمط الأسئلة وتتوقع أشكالًا جديدة، مع تقوية مهاراتك في المفردات والمعاني.\n\n'
                      'بالإضافة إلى ذلك، هناك ملازم عديدة تغطي القواعد، و وظائف اللغة ، وأهم الأساليب المستخدمة في الامتحان. إذا أتقنت هذه الأمور، فبإذن الله ستتمكن من اجتياز الامتحان بنجاح وتحقيق أعلى الدرجات.'
                    ),
                    const SizedBox(height: 20),
                    _buildTipButton(
                      context,
                      'فترة الحجز على الامتحان الرسمي',
                      CupertinoIcons.calendar,
                      'الفترة النهائية للحجز على الامتحان غير معروفة وعادةً ما تكون قبل امتحان التنافسي بأسبوع. جميع هذه الأمور والتعليمات يتم إصدارها من الوزارة ونحن ليس لنا أي علاقة بها، لذا يرجى متابعة المواقع الرسمية للوزارة لمعرفة آخر التعليمات.'
                    ),
                    const SizedBox(height: 20),
                    _buildTipButton(
                      context,
                      'مدة شهادة الامتحان بعد النجاح',
                      CupertinoIcons.doc_checkmark,
                      'مدة الشهادة بعد الامتحان لمدة سنتين، يمكن للمتقدم التقديم على التنافسي والماجستير في أي فترة خلال هذه السنتين.'
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
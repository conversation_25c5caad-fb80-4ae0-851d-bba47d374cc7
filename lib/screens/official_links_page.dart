import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher.dart';

class OfficialLinksPage extends StatelessWidget {
  const OfficialLinksPage({super.key});

  Future<void> _launchURL() async {
    final Uri url = Uri.parse('https://ints-auth.uob.edu.iq/');
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw 'Could not launch $url';
    }
  }

  Future<void> _launchTelegramChannel() async {
    final Uri url = Uri.parse('https://t.me/kfaapp');
    try {
      if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
        throw 'Could not launch $url';
      }
    } catch (e) {
      print('Error launching URL: $e');
    }
  }

  Widget _buildLinkButton(BuildContext context, String title, IconData icon, {VoidCallback? onPressed}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
      child: CupertinoButton(
        onPressed: onPressed ?? _launchURL,
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(15),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.blue.shade800,
              size: 30,
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'الروابط الرسمية للامتحانات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 20,
            color: Colors.blue.shade800,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        color: Colors.white,
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLinkButton(context, 'التسجيل في امتحان اللغة الإنكليزية', CupertinoIcons.globe),
                  const SizedBox(height: 20),
                  _buildLinkButton(context, 'التسجيل في امتحان اللغة العربية', CupertinoIcons.textbox),
                  const SizedBox(height: 20),
                  _buildLinkButton(context, 'التسجيل في امتحان الحاسوب', CupertinoIcons.desktopcomputer),
                  const SizedBox(height: 20),
                  _buildLinkButton(
                    context, 
                    ' قناتنا على التليغرام لنشر الأخبار ', 
                    Icons.telegram,
                    onPressed: _launchTelegramChannel
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
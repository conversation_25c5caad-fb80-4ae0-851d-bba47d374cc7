import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:intp_iraq/screens/home_page.dart';
import 'package:intp_iraq/screens/premium_subscription_page.dart';
import 'package:intp_iraq/services/subscription_service.dart';

class ExamResultPage extends StatefulWidget {
  final int score;
  final int totalQuestions;
  final String examType;

  const ExamResultPage({
    Key? key,
    required this.score,
    required this.totalQuestions,
    required this.examType,
  }) : super(key: key);

  @override
  _ExamResultPageState createState() => _ExamResultPageState();
}

class _ExamResultPageState extends State<ExamResultPage> {
  double get _finalScore => (widget.score / widget.totalQuestions) * 100;

  Future<void> _handleRetakeExam() async {
    bool isPremium = await SubscriptionService.isPremiumUser();
    if (!isPremium) {
      bool canTake = await SubscriptionService.canTakeExam(widget.examType);
      if (!canTake) {
        if (!mounted) return;
        await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PremiumSubscriptionPage())
        );
        return;
      }
      await SubscriptionService.recordExamAttempt(widget.examType);
    }

    if (!mounted) return;
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => HomePage()),
      (Route<dynamic> route) => false,
    );
  }

  String _getResultMessage() {
    if (_finalScore >= 90) return 'ممتاز! أداء رائع';
    if (_finalScore >= 80) return 'جيد جداً! استمر في التقدم';
    if (_finalScore >= 70) return 'جيد! يمكنك التحسن أكثر';
    if (_finalScore >= 60) return 'مقبول. حاول مرة أخرى للتحسين';
    return 'تحتاج إلى مزيد من الممارسة';
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => HomePage()),
          (Route<dynamic> route) => false,
        );
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'نتيجة الامتحان',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 24,
              color: Colors.white,
            ),
          ),
          backgroundColor: Color(0xFF1565C0),
          centerTitle: true,
          automaticallyImplyLeading: false,
        ),
        body: Center(
          child: Card(
            elevation: 8,
            margin: EdgeInsets.all(20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getResultMessage(),
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20),
                  Text(
                    'لقد أجبت على ${widget.score} من أصل ${widget.totalQuestions} سؤال بشكل صحيح',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 18,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20),
                  Text(
                    '${_finalScore.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: _finalScore >= 60 ? Colors.green : Colors.red,
                    ),
                  ),
                  SizedBox(height: 30),
                  ElevatedButton(
                    onPressed: _handleRetakeExam,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade800,
                      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      'إعادة الامتحان',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 18,
                      ),
                    ),
                  ),
                  SizedBox(height: 15),
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(builder: (context) => HomePage()),
                        (Route<dynamic> route) => false,
                      );
                    },
                    child: Text(
                      'العودة للصفحة الرئيسية',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
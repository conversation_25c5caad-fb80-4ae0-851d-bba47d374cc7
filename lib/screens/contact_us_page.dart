import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactUsPage extends StatelessWidget {
  const ContactUsPage({super.key});

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );
    if (!await launchUrl(emailUri)) {
      throw Exception('Could not launch email');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'تواصل معنا',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 24,
            color: Colors.blue.shade800,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        width: double.infinity,
        color: Colors.white,
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: 40),
                    _buildContactButton(
                      context,
                      ' تواصل معنا عبر الواتس اب',
                      CupertinoIcons.chat_bubble_fill,
                      () => _launchURL('https://wa.me/9647734837933'),
                    ),
                    SizedBox(height: 20),
                    _buildContactButton(
                      context,
                      'تواصل معنا عبر تيليجرام',
                      CupertinoIcons.paperplane,
                      () => _launchURL('https://t.me/T657k'),
                    ),
                    SizedBox(height: 20),
                    _buildContactButton(
                      context,
                      'تواصل معنا عبر البريد الإلكتروني',
                      CupertinoIcons.mail,
                      () => _launchEmail('<EMAIL>'),
                    ),
                    SizedBox(height: 20),
                    _buildContactButton(
                      context,
                      'تابع صفحتنا على الانستقرام',
                      CupertinoIcons.photo_camera,
                      () => _launchURL('https://www.instagram.com/musaid_kafaa?igsh=MTZ5NWM4azJka2c1cg%3D%3D&utm_source=qr'),
                    ),
                    SizedBox(height: 40),
                    SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactButton(BuildContext context, String title, IconData icon,
      VoidCallback onPressed) {
    return ElevatedButton.icon(
      icon: Icon(icon, color: Colors.white),
      label: Text(
        title,
        style:
            TextStyle(fontFamily: 'Cairo', fontSize: 18, color: Colors.white),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue.shade800,
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      onPressed: onPressed,
    );
  }
}

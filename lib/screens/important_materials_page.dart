import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:intp_iraq/screens/premium_subscription_page.dart';
import 'package:intp_iraq/services/subscription_service.dart';
// إضافة استيرادات Firebase
import 'package:firebase_storage/firebase_storage.dart';
// إضافة استيراد لمكتبات تعامل مع ملفات PDF
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:math' show sin; // إضافة لاستخدام دالة sine للرسوم المتحركة

class ImportantMaterialsPage extends StatefulWidget {
  const ImportantMaterialsPage({super.key});
  
  @override
  _ImportantMaterialsPageState createState() => _ImportantMaterialsPageState();
}

class _ImportantMaterialsPageState extends State<ImportantMaterialsPage> {
  bool _isPremium = false;
  bool _isLoading = true;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  
  @override
  void initState() {
    super.initState();
    _checkPremiumStatus();
  }
  
  Future<void> _checkPremiumStatus() async {
    bool isPremium = await SubscriptionService.isPremiumUser();
    if (mounted) {
      setState(() {
        _isPremium = isPremium;
        _isLoading = false;
      });
    }
  }

  // الدالة المعدلة لفتح الروابط
  Future<void> _launchURL(String url, {bool useExternalBrowser = false}) async {
    // إذا تم طلب استخدام متصفح خارجي (مثل Telegram)، استخدم url_launcher
    if (useExternalBrowser) {
      final Uri uri = Uri.parse(url);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        throw Exception('Could not launch $url');
      }
    } else {
      // استخدم مكتبة flutter_inappwebview لفتح الروابط داخل التطبيق
      _openInAppWebView(url);
    }
  }
  
  // دالة معدلة لفتح المتصفح الداخلي مع إضافة مؤشر التحميل
  void _openInAppWebView(String url) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'المحتوى',
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 20,
                color: Colors.blue.shade800,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.white,
            elevation: 0,
          ),
          body: Stack(
            children: [
              InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(url)),
                initialOptions: InAppWebViewGroupOptions(
                  crossPlatform: InAppWebViewOptions(
                    javaScriptEnabled: true,
                    useShouldOverrideUrlLoading: true,
                  ),
                ),
                onLoadStart: (controller, url) {},
                onLoadStop: (controller, url) {},
                onProgressChanged: (controller, progress) {},
                shouldOverrideUrlLoading: (controller, navigationAction) async {
                  return NavigationActionPolicy.ALLOW;
                },
              ),
              // إضافة مؤشر التحميل الذي سيظهر حتى تكتمل الصفحة
              Builder(
                builder: (context) {
                  return FutureBuilder<bool>(
                    future: Future.delayed(Duration(milliseconds: 500), () => true),
                    builder: (context, snapshot) {
                      return Positioned.fill(
                        child: StreamBuilder<LoadedResource>(
                          stream: _createProgressStream(),
                          builder: (context, snapshot) {
                            if (!snapshot.hasData || snapshot.data?.progress != 100) {
                              return Container(
                                color: Colors.white,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CircularProgressIndicator(
                                        color: Colors.blue.shade800,
                                      ),
                                      SizedBox(height: 20),
                                      Text(
                                        'جاري تحميل الملزمة...',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 16,
                                          color: Colors.blue.shade800,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }
                            return SizedBox.shrink(); // إخفاء مؤشر التحميل بعد اكتمال التحميل
                          },
                        ),
                      );
                    },
                  );
                }
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة مساعدة لإنشاء تدفق يمثل حالة تحميل المورد
  Stream<LoadedResource> _createProgressStream() {
    return Stream.periodic(Duration(milliseconds: 100), (i) {
      // تحميل يستمر حتى 3 ثوانٍ كحد أقصى
      int progress = (i + 1) * 10;
      if (progress > 100) progress = 100;
      return LoadedResource(progress);
    }).take(11); // أخذ 11 خطوة (0% إلى 100%)
  }

  // دالة جديدة لإنشاء مؤشر التمرير للأسفل
  Widget _buildScrollIndicator() {
    return Column(
      children: [
        SizedBox(height: 4),
        Text(
          'مرر للأسفل لعرض المزيد',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4),
        // إضافة أيقونة متحركة للتمرير للأسفل
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0, end: 4),
          duration: Duration(seconds: 1),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, sin(value) * 4),
              child: Icon(
                CupertinoIcons.chevron_down,
                color: Colors.blue.shade800,
                size: 20,
              ),
            );
          },
        ),
      ],
    );
  }

  // دالة معدلة لعرض ملفات PDF من مجلد معين في Firebase Storage
  Future<void> _showPDFFilesFromStorage(String folderPath, String categoryTitle) async {
    try {
      print("Attempting to list files in: $folderPath");
      
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: Colors.blue.shade800),
                  SizedBox(width: 20),
                  Text(
                    "جاري تحميل الملفات...",
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );

      try {
        // التأكد من أن folderPath لا ينتهي بشرطة مائلة
        if (folderPath.endsWith('/')) {
          folderPath = folderPath.substring(0, folderPath.length - 1);
        }
        
        // الحصول على قائمة الملفات من Firebase Storage
        final ListResult result = await _storage.ref(folderPath).listAll();
        
        // إغلاق مؤشر التحميل
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        
        print("Files found: ${result.items.length}");
        
        if (result.items.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'لا توجد ملفات متاحة حالياً',
                style: TextStyle(fontFamily: 'Cairo'),
                textAlign: TextAlign.center,
              ),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        // عرض قائمة الملفات في نافذة منبثقة
        showModalBottomSheet(
          context: context,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          isScrollControlled: true,
          backgroundColor: Colors.white,
          builder: (context) {
            // تعريف ScrollController للتحكم بالتمرير
            final ScrollController scrollController = ScrollController();
            
            return Container(
              height: MediaQuery.of(context).size.height * 0.85,
              padding: EdgeInsets.all(16),
              child: Stack(
                children: [
                  Column(
                    children: [
                      Text(
                        categoryTitle,
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      // استخدام مؤشر التمرير المخصص
                      if (result.items.length > 3)
                        _buildScrollIndicator(),
                      
                      // قائمة الملفات بدون شريط تمرير على الجانب
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          physics: BouncingScrollPhysics(),
                          itemCount: result.items.length,
                          itemBuilder: (context, index) {
                            final Reference fileRef = result.items[index];
                            final String fileName = fileRef.name;
                            
                            return Card(
                              margin: EdgeInsets.symmetric(horizontal: 0, vertical: 6),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: ListTile(
                                contentPadding: EdgeInsets.all(10),
                                leading: Icon(
                                  Icons.picture_as_pdf,
                                  color: Colors.red,
                                  size: 40,
                                ),
                                title: Text(
                                  // إزالة امتداد الملف من الاسم المعروض إذا كان موجوداً
                                  fileName.toLowerCase().endsWith('.pdf') 
                                    ? fileName.substring(0, fileName.length - 4)
                                    : fileName,
                                  style: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                onTap: () {
                                  print("Tapped on file: ${fileRef.fullPath}");
                                  _openPDFFile(fileRef);
                                },
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  // زر التمرير للأسفل
                  if (result.items.length > 5)
                    Positioned(
                      bottom: 20,
                      right: 20,
                      child: GestureDetector(
                        onTap: () {
                          // التمرير بسلاسة للأسفل
                          scrollController.animateTo(
                            scrollController.position.maxScrollExtent,
                            duration: Duration(milliseconds: 800),
                            curve: Curves.easeInOut,
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade800,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 5,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            CupertinoIcons.arrow_down,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        );
        
      } catch (e) {
        // إغلاق مؤشر التحميل إذا حدث خطأ
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        print("Firebase Storage Error: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحميل الملفات: $e',
              style: TextStyle(fontFamily: 'Cairo'),
              textAlign: TextAlign.center,
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // للتأكد من إغلاق مؤشر التحميل في حالة حدوث أي خطأ آخر
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      print("General Error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ غير متوقع',
            style: TextStyle(fontFamily: 'Cairo'),
            textAlign: TextAlign.center,
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // دالة معدلة لفتح ملف PDF مع استخدام عارض PDF داخلي
  void _openPDFFile(Reference fileRef) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Colors.blue.shade800),
                SizedBox(width: 20),
                Text(
                  "جاري تحضير الملف...",
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      fileRef.getDownloadURL().then((downloadUrl) async {
        print("Download URL: $downloadUrl");
        
        try {
          // الحصول على حجم الملف قبل التنزيل إذا أمكن
          final metadata = await fileRef.getMetadata();
          final fileSize = metadata.size ?? 0;
          
          // إذا كان الملف كبيرًا، عرض تنبيه للمستخدم
          if (fileSize > 15 * 1024 * 1024) { // أكبر من 15 ميغابايت
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'الملف كبير الحجم، قد يستغرق التحميل بعض الوقت',
                  style: TextStyle(fontFamily: 'Cairo'),
                  textAlign: TextAlign.center,
                ),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 2),
              ),
            );
          }

          // تنزيل الملف
          final response = await http.get(Uri.parse(downloadUrl));
          
          // الحصول على مسار مؤقت لحفظ الملف
          final tempDir = await getTemporaryDirectory();
          final filePath = '${tempDir.path}/${fileRef.name}';
          
          // حفظ الملف
          final file = File(filePath);
          await file.writeAsBytes(response.bodyBytes);
          
          // إغلاق مؤشر التحميل
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          
          // إغلاق قائمة الملفات إذا كانت مفتوحة
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          
          // فتح عارض الـ PDF
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PDFViewerPage(
                filePath: filePath,
                fileName: fileRef.name.replaceAll('.pdf', ''),
              ),
            ),
          );
        } catch (e) {
          print("Error downloading PDF: $e");
          // إغلاق مؤشر التحميل
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          
          // عرض رسالة خطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ أثناء تنزيل الملف: $e',
                style: TextStyle(fontFamily: 'Cairo'),
                textAlign: TextAlign.center,
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }).catchError((error) {
        print("Error getting download URL: $error");
        // إغلاق مؤشر التحميل
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل تحميل الملف، يرجى المحاولة مرة أخرى',
              style: TextStyle(fontFamily: 'Cairo'),
              textAlign: TextAlign.center,
            ),
            backgroundColor: Colors.red,
          ),
        );
      });
    } catch (e) {
      print("Exception in _openPDFFile: $e");
      // إغلاق مؤشر التحميل في حالة وجود استثناء
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ أثناء فتح الملف',
            style: TextStyle(fontFamily: 'Cairo'),
            textAlign: TextAlign.center,
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildMaterialButton(BuildContext context, String title, IconData icon, [String? url]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
      child: CupertinoButton(
        onPressed: () {
          // Check if user is premium before accessing any material
          if (!_isPremium) {
            Navigator.push(context, CupertinoPageRoute(
              builder: (context) => PremiumSubscriptionPage()
            ));
            return;
          }
          
          try {
            // Original logic for premium users
            if (title == 'مواد إضافية مساعدة') {
              _showAdditionalMaterialsDialog(context);
            } else if (title == 'القطع الخارجية الخاصة باللغة الإنكليزية') {
              _showPDFFilesFromStorage('english_passages', 'القطع الخارجية الخاصة باللغة الإنكليزية');
            } else if (title == 'ملازم امتحان الحاسوب') {
              _showPDFFilesFromStorage('computer_mlazim', 'ملازم امتحان الحاسوب');
            } else if (title == 'ملازم اللغة الإنكليزية') {
              _showPDFFilesFromStorage('english_mlazim', 'ملازم اللغة الإنكليزية');
            } else if (title == 'ملازم اللغة العربية') {
              _showPDFFilesFromStorage('arabic_mlazim', 'ملازم اللغة العربية');
            } else if (url != null) {
              _launchURL(url);
            }
          } catch (e) {
            print("Button Error: $e");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'حدث خطأ، يرجى المحاولة مرة أخرى',
                  style: TextStyle(fontFamily: 'Cairo'),
                  textAlign: TextAlign.center,
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(15),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.blue.shade800,
              size: 30,
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // Add premium indicator icon
            if (!_isPremium) Icon(
              Icons.workspace_premium,
              color: Colors.amber,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
  
  // Dialog for additional materials
  void _showAdditionalMaterialsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('تواصل معنا',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'للحصول على المواد الإضافية المساعدة، يرجى التواصل معنا عبر التليغرام لإضافتك في القناة الخاصة',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: 16,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('إلغاء',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  color: Colors.red,
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            CupertinoDialogAction(
              child: const Text('فتح التليغرام',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  color: Colors.blue,
                ),
              ),
              onPressed: () {
                _launchURL('https://t.me/T657k', useExternalBrowser: true);
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'الملازم والمواد المهمة',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 20,
            color: Colors.blue.shade800,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading 
          ? Center(child: CircularProgressIndicator())
          : Container(
              color: Colors.white,
              child: SafeArea(
                child: Center(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildMaterialButton(
                          context,
                          'ملازم اللغة الإنكليزية',
                          CupertinoIcons.book_fill,
                          'https://drive.google.com/drive/folders/1urozs0KAsY-DBHAWKYuIcv70QanSKlBe?usp=drive_link'
                        ),
                        const SizedBox(height: 20),
                        _buildMaterialButton(
                          context,
                          'القطع الخارجية الخاصة باللغة الإنكليزية',
                          CupertinoIcons.doc_text_search,
                        ),
                        const SizedBox(height: 20),
                        _buildMaterialButton(
                          context,
                          'ملازم اللغة العربية',
                          CupertinoIcons.text_quote,
                          'https://drive.google.com/drive/folders/1sogDdvYg2iyeF_M9lKNwekii1X5ss3Q9?usp=sharing'
                        ),
                        const SizedBox(height: 20),
                        _buildMaterialButton(
                          context,
                          'ملازم امتحان الحاسوب',
                          CupertinoIcons.desktopcomputer,
                        ),
                        const SizedBox(height: 20),
                        _buildMaterialButton(
                          context,
                          'مواد إضافية مساعدة',
                          CupertinoIcons.doc_text_fill
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}

// فئة مساعدة لتتبع تقدم التحميل
class LoadedResource {
  final int progress; // قيمة بين 0 و 100
  LoadedResource(this.progress);
}

// إضافة صفحة جديدة لعرض ملفات PDF
class PDFViewerPage extends StatefulWidget {
  final String filePath;
  final String fileName;

  const PDFViewerPage({required this.filePath, required this.fileName, Key? key}) : super(key: key);

  @override
  _PDFViewerPageState createState() => _PDFViewerPageState();
}

class _PDFViewerPageState extends State<PDFViewerPage> {
  int _totalPages = 0;
  int _currentPage = 0;
  bool _isLoading = true;
  PDFViewController? _pdfViewController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(CupertinoIcons.back, color: Colors.blue.shade800),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.fileName,
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 20,
            color: Colors.blue.shade800,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (_totalPages > 0) 
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Text(
                  '${_currentPage + 1} / $_totalPages',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    color: Colors.blue.shade800,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          Container(
            color: Colors.white,
            child: PDFView(
              filePath: widget.filePath,
              enableSwipe: true,
              swipeHorizontal: false,
              autoSpacing: false,
              pageFling: true,
              pageSnap: true,
              fitPolicy: FitPolicy.WIDTH,
              defaultPage: _currentPage,
              onRender: (pages) {
                setState(() {
                  _totalPages = pages!;
                  _isLoading = false;
                });
              },
              onViewCreated: (PDFViewController pdfViewController) {
                setState(() {
                  _pdfViewController = pdfViewController;
                });
              },
              onError: (error) {
                print("PDF Error: $error");
                setState(() {
                  _isLoading = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'حدث خطأ أثناء عرض الملف',
                      style: TextStyle(fontFamily: 'Cairo'),
                      textAlign: TextAlign.center,
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              onPageError: (page, error) {
                print("PDF Page Error - Page: $page, Error: $error");
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'حدث خطأ في الصفحة $page',
                      style: TextStyle(fontFamily: 'Cairo'),
                      textAlign: TextAlign.center,
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              onPageChanged: (int? page, int? total) {
                if (page != null) {
                  setState(() {
                    _currentPage = page;
                  });
                }
              },
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.white,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.blue.shade800,
                    ),
                    SizedBox(height: 20),
                    Text(
                      'جاري تحميل الملزمة...',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontSize: 16,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (_totalPages > 1 && !_isLoading)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(50),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          CupertinoIcons.arrow_up_to_line,
                          color: Colors.blue.shade800,
                        ),
                        onPressed: _currentPage == 0
                            ? null
                            : () {
                                _pdfViewController?.setPage(0);
                              },
                      ),
                      IconButton(
                        icon: Icon(
                          CupertinoIcons.chevron_up,
                          color: Colors.blue.shade800,
                        ),
                        onPressed: _currentPage == 0
                            ? null
                            : () {
                                _pdfViewController?.setPage(_currentPage - 1);
                              },
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${_currentPage + 1} / $_totalPages',
                          style: TextStyle(
                            fontFamily: 'Cairo',
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          CupertinoIcons.chevron_down,
                          color: Colors.blue.shade800,
                        ),
                        onPressed: _currentPage == _totalPages - 1
                            ? null
                            : () {
                                _pdfViewController?.setPage(_currentPage + 1);
                              },
                      ),
                      IconButton(
                        icon: Icon(
                          CupertinoIcons.arrow_down_to_line,
                          color: Colors.blue.shade800,
                        ),
                        onPressed: _currentPage == _totalPages - 1
                            ? null
                            : () {
                                _pdfViewController?.setPage(_totalPages - 1);
                              },
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // تنظيف الملفات المؤقتة عند الخروج من الصفحة
  @override
  void dispose() {
    try {
      File(widget.filePath).delete().catchError((error) {
        print("Error deleting temporary file: $error");
        // إضافة إرجاع لمعالجة التحذير
        return File(widget.filePath);
      });
    } catch (e) {
      print("Error during file cleanup: $e");
    }
    super.dispose();
  }
}
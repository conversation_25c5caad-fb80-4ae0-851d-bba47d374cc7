import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intp_iraq/screens/login_page.dart'; // استيراد صفحة تسجيل الدخول

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _username = '';
  String _email = '';
  String _phone = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // التعامل مع نوعي الحسابات: العادي وحساب جوجل
        final isGoogleSignIn = user.providerData
            .any((element) => element.providerId == 'google.com');
        
        DocumentSnapshot? userData;
        
        if (isGoogleSignIn) {
          // للمستخدمين عبر Google، نبحث عن طريق البريد الإلكتروني
          final userDocs = await FirebaseFirestore.instance.collection('users')
              .where('email', isEqualTo: user.email)
              .limit(1)
              .get();
              
          if (userDocs.docs.isNotEmpty) {
            userData = userDocs.docs.first;
          }
        } else {
          // للمستخدمين العاديين، نستخدم معرف المستخدم
          userData = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();
        }
        
        if (userData != null && userData.exists) {
          final data = userData.data() as Map<String, dynamic>;
          setState(() {
            _username = data['username'] ?? '';
            _email = user.email ?? '';
            _phone = data['phone'] ?? ''; // هنا سيكون فارغاً إذا كان غير موجود
          });
        }
      }
    } catch (e) {
      print('Error loading user data: $e');
      // إذا حدث خطأ، نضع قيمًا افتراضية
      setState(() {
        _phone = '';
      });
    }
  }

  Future<void> _deleteAccount() async {
    final navigatorContext = Navigator.of(context).context;
    
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CupertinoAlertDialog(
          title: Text('حذف الحساب', style: TextStyle(fontFamily: 'Cairo')),
          content: Text('هل أنت متأكد؟ سيتم حذف الحساب وجميع نتائج الامتحانات وبياناتك نهائياً', style: TextStyle(fontFamily: 'Cairo')),
          actions: <Widget>[
            CupertinoDialogAction(
              child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            CupertinoDialogAction(
              child: Text('حذف الحساب', style: TextStyle(fontFamily: 'Cairo', color: Colors.red)),
              onPressed: () async {
                try {
                  Navigator.of(dialogContext).pop();
                  
                  // تعديل شكل مربع "جاري الحذف"
                  showDialog(
                    context: navigatorContext,
                    barrierDismissible: false,
                    builder: (BuildContext loadingContext) {
                      return Dialog(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        backgroundColor: Colors.white,
                        elevation: 5,
                        child: Container(
                          padding: EdgeInsets.all(20),
                          width: 200,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 50,
                                height: 50,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade900),
                                  strokeWidth: 3,
                                ),
                              ),
                              SizedBox(height: 20),
                              Text(
                                '...جاري الحذف',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade900,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                  
                  User? user = FirebaseAuth.instance.currentUser;
                  if (user != null) {
                    final String uid = user.uid;
                    final String? email = user.email;
                    
                    // حفظ البيانات في مجموعة deleted_emails
                    await FirebaseFirestore.instance.collection('deleted_emails').add({
                      'email': email,
                      'uid': uid,
                      'deletedAt': FieldValue.serverTimestamp(),
                    });
                    
                    // تأخير وهمي لمحاكاة عملية الحذف
                    await Future.delayed(Duration(seconds: 2));
                    
                    // إغلاق شكل التحميل باستخدام السياق الثابت
                    Navigator.of(navigatorContext).pop();
                    
                    // عرض رسالة النجاح باستخدام السياق الثابت
                    await showDialog(
                      context: navigatorContext,
                      barrierDismissible: false,
                      builder: (BuildContext successContext) {
                        return CupertinoAlertDialog(
                          title: Text('تم بنجاح', style: TextStyle(fontFamily: 'Cairo')),
                          content: Text('تم حذف جميع البيانات بنجاح', style: TextStyle(fontFamily: 'Cairo')),
                          actions: [
                            CupertinoDialogAction(
                              child: Text('حسناً', style: TextStyle(fontFamily: 'Cairo')),
                              onPressed: () {
                                Navigator.of(successContext).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                    
                    // تسجيل الخروج
                    await FirebaseAuth.instance.signOut();
                    
                    // العودة إلى صفحة تسجيل الدخول باستخدام السياق الثابت
                    Navigator.of(navigatorContext).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (context) => LoginPage()),
                      (route) => false,
                    );
                  }
                } catch (e) {
                  print('Error during account operation: $e');
                  
                  // عرض رسالة خطأ للمستخدم باستخدام السياق الثابت
                  ScaffoldMessenger.of(navigatorContext).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء العملية. الرجاء المحاولة مرة أخرى.', 
                        style: TextStyle(fontFamily: 'Cairo'))
                    )
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue.shade900,
        leading: IconButton(
          icon: Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        iconTheme: IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: Center(
                child: Container(
                  constraints: BoxConstraints(maxWidth: 600),
                  padding: EdgeInsets.all(8), // Reduced from 16 to 8
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 3, // Reduced from 5 to 3
                          blurRadius: 5, // Reduced from 7 to 5
                          offset: Offset(0, 2), // Reduced from Offset(0, 3) to Offset(0, 2)
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(12), // Reduced from 16 to 12
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: CircleAvatar(
                            radius: constraints.maxWidth * 0.1,
                            backgroundColor: Colors.blue.shade900,
                            child: Icon(CupertinoIcons.person_alt, size: constraints.maxWidth * 0.12, color: Colors.white),
                          ),
                        ),
                        SizedBox(height: 20),
                        Center(
                          child: Text(
                            'معلومات المستخدم',
                            style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.06, fontWeight: FontWeight.bold, color: Colors.blue.shade900),
                          ),
                        ),
                        SizedBox(height: 20),
                        _buildInfoCard('اسم المستخدم', _username, CupertinoIcons.person, constraints),
                        SizedBox(height: 10),
                        _buildInfoCard('البريد الإلكتروني', _email, CupertinoIcons.mail, constraints),
                        SizedBox(height: 10),
                        _buildInfoCard('رقم الهاتف', _phone, CupertinoIcons.phone, constraints),
                        SizedBox(height: 30),
                        Center(
                          child: ElevatedButton(
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return CupertinoAlertDialog(
                                    title: Text('تعديل المعلومات', style: TextStyle(fontFamily: 'Cairo')),
                                    content: Text('لتعديل معلوماتك الشخصية، يرجى التواصل مع الدعم الفني عبر التيلغرام', style: TextStyle(fontFamily: 'Cairo')),
                                    actions: <Widget>[
                                      CupertinoDialogAction(
                                        child: Text('إلغاء', style: TextStyle(fontFamily: 'Cairo')),
                                        onPressed: () => Navigator.of(context).pop(),
                                      ),
                                      CupertinoDialogAction(
                                        child: Text('فتح التيلغرام', style: TextStyle(fontFamily: 'Cairo', color: Colors.blue)),
                                        onPressed: () async {
                                          final Uri uri = Uri.parse('https://t.me/T657k');
                                          if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
                                            throw Exception('Could not launch telegram link');
                                          }
                                          Navigator.of(context).pop();
                                        },
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade900,
                              padding: EdgeInsets.symmetric(horizontal: constraints.maxWidth * 0.1, vertical: constraints.maxWidth * 0.03),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
                            ),
                            child: Text('تعديل المعلومات', style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.04, color: Colors.white)),
                          ),
                        ),
                        SizedBox(height: 20),
                        Center(
                          child: TextButton(
                            onPressed: _deleteAccount,
                            child: Text('حذف الحساب', style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.04, color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon, BoxConstraints constraints) {
    // عرض "غير محدد" إذا كانت القيمة فارغة (خاصة لرقم الهاتف)
    String displayValue = value.isNotEmpty ? value : "غير محدد";
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade100,
          child: Icon(icon, color: Colors.blue.shade900, size: constraints.maxWidth * 0.05),
        ),
        title: Text(title, style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.035, color: Colors.grey[600])),
        subtitle: Text(displayValue, style: TextStyle(fontFamily: 'Cairo', fontSize: constraints.maxWidth * 0.04, fontWeight: FontWeight.bold)),
      ),
    );
  }
}
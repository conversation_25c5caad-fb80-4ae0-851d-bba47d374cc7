import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:intp_iraq/services/question_service.dart';
import 'package:intp_iraq/services/subscription_service.dart';
import 'package:intp_iraq/screens/premium_subscription_page.dart';
import 'package:intp_iraq/screens/home_page.dart';
import 'dart:async';


class ArabicExamPage extends StatefulWidget {
 const ArabicExamPage({super.key});


 @override
 State<ArabicExamPage> createState() => _ArabicExamPageState();
}


class _ArabicExamPageState extends State<ArabicExamPage> with TickerProviderStateMixin {
 bool _mounted = true;
 int _currentQuestionIndex = 0;
 int _score = 0;
 bool _showResult = false;
 List<Map<String, dynamic>> _questions = [];
 List<int?> _userAnswers = [];
 bool _isLoading = true;
 bool _error = false;
 int _remainingTime = 3600;
 Timer? _timer;


 late AnimationController _fadeController;
 late Animation<double> _fadeAnimation;

 @override
 void initState() {
   super.initState();
   _fadeController = AnimationController(
     duration: Duration(milliseconds: 800),
     vsync: this,
   );
   _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
     CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
   );
   _loadQuestions();
   _startTimer();
 }


 void _startTimer() {
   _timer = Timer.periodic(Duration(seconds: 1), (timer) {
     if (_mounted) {
       setState(() {
         if (_remainingTime > 0) {
           _remainingTime--;
         } else {
           _timer?.cancel();
           _calculateScore();
           _showResult = true;
         }
       });
     } else {
       _timer?.cancel();
     }
   });
 }


 @override
 void dispose() {
   _mounted = false;
   _timer?.cancel();
   _fadeController.dispose();
   super.dispose();
 }

 Future<void> _loadQuestions() async {
   if (!_mounted) return;
   setState(() {
     _isLoading = true;
     _error = false;
   });


   try {
     List<Map<String, dynamic>> allQuestions =
         await QuestionService.getQuestions('arabic');
     if (_mounted) {
       setState(() {
         allQuestions.shuffle();
         _questions = allQuestions.take(40).toList();
         _userAnswers = List.filled(_questions.length, null);
         _isLoading = false;
       });
       _fadeController.forward();
     }
   } catch (e) {
     debugPrint('Error loading questions: $e');
     if (_mounted) {
       setState(() {
         _isLoading = false;
         _error = true;
       });
     }
   }
 }

  void _answerQuestion(int selectedAnswer) {
    if (_mounted) {
      setState(() {
        _userAnswers[_currentQuestionIndex] = selectedAnswer;
      });
    }
  }

  void _goToNextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1 && _mounted) {
      setState(() => _currentQuestionIndex++);
    }
  }

  void _goToPreviousQuestion() {
    if (_currentQuestionIndex > 0 && _mounted) {
      setState(() => _currentQuestionIndex--);
    }
  }

  void _calculateScore() {
    _score = 0;
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers[i] == _questions[i]['correctAnswer']) {
        _score++;
      }
    }
  }

  Future<void> _resetQuiz() async {
    String examType = 'arabic'; // تحديد نوع الامتحان
    
    try {
      bool isPremium = await SubscriptionService.isPremiumUser();
      if (!isPremium) {
        bool canTake = await SubscriptionService.canTakeExam(examType);
        if (!canTake) {
          if (!mounted) return;
          // تعديل ليفتح صفحة الاشتراك المدفوع مع تمرير معلومة أنه قادم من صفحة نتائج الامتحان
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PremiumSubscriptionPage(
                onReturn: () {
                  // عند الرجوع، سيتم توجيه المستخدم إلى الصفحة الرئيسية
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(builder: (context) => HomePage()),
                  );
                }
              ),
            )
          );
          return;
        }
        await SubscriptionService.recordExamAttempt(examType);
      }

      if (mounted) {
        setState(() {
          _currentQuestionIndex = 0;
          _score = 0;
          _showResult = false;
          _questions.shuffle();
          _userAnswers = List.filled(_questions.length, null);
          _remainingTime = 3600;
        });
        _startTimer();
      }
    } catch (e) {
      debugPrint('Error checking exam permission: $e');
      if (!mounted) return;
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PremiumSubscriptionPage(
            onReturn: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => HomePage()),
              );
            }
          ),
        )
      );
    }
  }

  double get _finalScore => (_score / _questions.length) * 100;

 @override
 Widget build(BuildContext context) {
   return Scaffold(
     backgroundColor: Color(0xFFF5F7FA),
     appBar: _buildAppBar(),
     body: Container(
       width: double.infinity,
       height: double.infinity,
       decoration: BoxDecoration(
         gradient: LinearGradient(
           begin: Alignment.topCenter,
           end: Alignment.bottomCenter,
           colors: [
             Color(0xFFF5F7FA),
             Color(0xFFE8F2F7),
             Color(0xFFD4E6F1),
           ],
         ),
       ),
       child: _isLoading
           ? Center(
               child: Column(
                 mainAxisAlignment: MainAxisAlignment.center,
                 children: [
                   CircularProgressIndicator(
                     valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E86AB)),
                     strokeWidth: 3,
                   ),
                   SizedBox(height: 16),
                   Text(
                     'جاري تحميل الأسئلة...',
                     style: TextStyle(
                       fontFamily: 'Cairo',
                       fontSize: 16,
                       color: Color(0xFF2E86AB),
                     ),
                   ),
                 ],
               ),
             )
           : _error
               ? _buildErrorWidget()
               : _showResult
                   ? _buildResultScreen()
                   : FadeTransition(
                       opacity: _fadeAnimation,
                       child: _buildQuestionScreen(),
                     ),
     ),
   );
 }

 AppBar _buildAppBar() {
   return AppBar(
     leading: IconButton(
       icon: Icon(CupertinoIcons.back, color: Colors.white, size: 22),
       onPressed: () {
         showDialog(
           context: context,
           barrierDismissible: false,
           builder: (BuildContext context) {
             return AlertDialog(
               backgroundColor: Colors.white,
               shape: RoundedRectangleBorder(
                 borderRadius: BorderRadius.circular(24),
               ),
               contentPadding: EdgeInsets.zero,
               content: SizedBox(
                 width: 320,
                 child: Column(
                   mainAxisSize: MainAxisSize.min,
                   children: [
                     // Content
                     Padding(
                       padding: EdgeInsets.all(32),
                       child: Column(
                         children: [
                           // Icon
                           Container(
                             width: 80,
                             height: 80,
                             decoration: BoxDecoration(
                               color: Colors.orange.shade100,
                               borderRadius: BorderRadius.circular(40),
                             ),
                             child: Icon(
                               CupertinoIcons.exclamationmark_triangle_fill,
                               color: Colors.orange.shade600,
                               size: 40,
                             ),
                           ),
                           SizedBox(height: 24),
                           // Main question
                           Text(
                             'هل أنت متأكد من رغبتك في ترك الامتحان؟',
                             style: TextStyle(
                               fontFamily: 'Cairo',
                               fontSize: 18,
                               color: Color(0xFF2C3E50),
                               fontWeight: FontWeight.bold,
                             ),
                             textAlign: TextAlign.center,
                           ),
                           SizedBox(height: 20),
                           // Warning text
                           Container(
                             padding: EdgeInsets.all(16),
                             decoration: BoxDecoration(
                               color: Colors.red.shade50,
                               borderRadius: BorderRadius.circular(12),
                               border: Border.all(
                                 color: Colors.red.shade200,
                                 width: 1,
                               ),
                             ),
                             child: Row(
                               children: [
                                 Icon(
                                   CupertinoIcons.info_circle_fill,
                                   color: Colors.red.shade600,
                                   size: 20,
                                 ),
                                 SizedBox(width: 12),
                                 Expanded(
                                   child: Text(
                                     'سيتم فقدان جميع إجاباتك والبدء من جديد',
                                     style: TextStyle(
                                       fontFamily: 'Cairo',
                                       fontSize: 14,
                                       color: Colors.red.shade700,
                                     ),
                                     textAlign: TextAlign.right,
                                   ),
                                 ),
                               ],
                             ),
                           ),
                           SizedBox(height: 32),
                           // Buttons
                           Row(
                             children: [
                               Expanded(
                                 child: TextButton(
                                   style: TextButton.styleFrom(
                                     backgroundColor: Color(0xFFF5F7FA),
                                     foregroundColor: Color(0xFF2C3E50),
                                     padding: EdgeInsets.symmetric(vertical: 16),
                                     shape: RoundedRectangleBorder(
                                       borderRadius: BorderRadius.circular(12),
                                       side: BorderSide(
                                         color: Color(0xFFE8F2F7),
                                         width: 1,
                                       ),
                                     ),
                                   ),
                                   onPressed: () {
                                     Navigator.of(context).pop();
                                   },
                                   child: Row(
                                     mainAxisAlignment: MainAxisAlignment.center,
                                     children: [
                                       Icon(CupertinoIcons.back, size: 16),
                                       SizedBox(width: 8),
                                       Text(
                                         'البقاء',
                                         style: TextStyle(
                                           fontFamily: 'Cairo',
                                           fontSize: 16,
                                           fontWeight: FontWeight.w600,
                                         ),
                                       ),
                                     ],
                                   ),
                                 ),
                               ),
                               SizedBox(width: 12),
                               Expanded(
                                 child: TextButton(
                                   style: TextButton.styleFrom(
                                     backgroundColor: const Color(0xFFFFEBEE),
                                     foregroundColor: Colors.red.shade700,
                                     padding: EdgeInsets.symmetric(vertical: 18),
                                     shape: RoundedRectangleBorder(
                                       borderRadius: BorderRadius.circular(16),
                                       side: BorderSide(
                                         color: const Color.fromARGB(255, 255, 255, 255),
                                         width: 1.5,
                                       ),
                                     ),
                                   ),
                                   onPressed: () {
                                     Navigator.of(context).pop();
                                     Navigator.of(context).pop();
                                   },
                                   child: Row(
                                     mainAxisAlignment: MainAxisAlignment.center,
                                     children: [
                                       Icon(CupertinoIcons.xmark_circle_fill,
                                            size: 18,
                                            color: Colors.red.shade700),
                                       SizedBox(width: 10),
                                       Text(
                                         'الخروج',
                                         style: TextStyle(
                                           fontFamily: 'Cairo',
                                           fontSize: 16,
                                           fontWeight: FontWeight.bold,
                                           color: Colors.red.shade700,
                                         ),
                                       ),
                                     ],
                                   ),
                                 ),
                               ),
                             ],
                           ),
                         ],
                       ),
                     ),
                   ],
                 ),
               ),
             );
           },
         );
       },
     ),
     centerTitle: true,
     title: Row(
       mainAxisSize: MainAxisSize.min,
       children: [
         Icon(CupertinoIcons.list_bullet, color: Colors.white, size: 24),
         SizedBox(width: 8),
         Text(
           'امتحان اللغة العربية',
           style: TextStyle(
             fontFamily: 'Cairo',
             fontSize: 20,
             color: Colors.white,
             fontWeight: FontWeight.bold,
           ),
         ),
       ],
     ),
     backgroundColor: Color(0xFF2E86AB),
     elevation: 0,
     flexibleSpace: Container(
       decoration: BoxDecoration(
         gradient: LinearGradient(
           begin: Alignment.topLeft,
           end: Alignment.bottomRight,
           colors: [Color(0xFF2E86AB), Color(0xFF1976D2)],
         ),
       ),
     ),
   );
 }

 Widget _buildErrorWidget() {
   return Center(
     child: Container(
       margin: EdgeInsets.all(20),
       padding: EdgeInsets.all(30),
       decoration: BoxDecoration(
         color: Colors.white,
         borderRadius: BorderRadius.circular(20),
         boxShadow: [
           BoxShadow(
             color: Colors.black.withValues(alpha: 0.1),
             blurRadius: 20,
             offset: Offset(0, 10),
           ),
         ],
       ),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         children: [
           Icon(CupertinoIcons.wifi_slash, size: 60, color: Colors.red.shade400),
           SizedBox(height: 20),
           Text(
             'حدث خطأ أثناء تحميل الأسئلة',
             style: TextStyle(
               color: Color(0xFF2E86AB),
               fontSize: 18,
               fontFamily: 'Cairo',
               fontWeight: FontWeight.bold,
             ),
             textAlign: TextAlign.center,
           ),
           SizedBox(height: 20),
           ElevatedButton(
             onPressed: _loadQuestions,
             style: ElevatedButton.styleFrom(
               backgroundColor: Color(0xFF2E86AB),
               foregroundColor: Colors.white,
               padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
               shape: RoundedRectangleBorder(
                 borderRadius: BorderRadius.circular(15),
               ),
             ),
             child: Text('إعادة المحاولة',
                 style: TextStyle(fontFamily: 'Cairo', fontSize: 16)),
           ),
         ],
       ),
     ),
   );
 }

 Widget _buildQuestionScreen() {
   return LayoutBuilder(
     builder: (BuildContext context, BoxConstraints constraints) {
       final double screenHeight = constraints.maxHeight;
       final double screenWidth = constraints.maxWidth;
       final bool isSmallScreen = screenWidth < 600;


       return SingleChildScrollView(
         child: Padding(
           padding: EdgeInsets.all(screenWidth * 0.04),
           child: Column(
             crossAxisAlignment: CrossAxisAlignment.stretch,
             children: [
               _buildTimerAndProgressWidget(),
               SizedBox(height: 20),
               _buildQuestionCard(screenHeight),
               SizedBox(height: 20),
               ..._buildAnswerButtons(screenWidth, isSmallScreen),
               SizedBox(height: 20),
               _buildNavigationButtons(screenWidth),
             ],
           ),
         ),
       );
     },
   );
 }

 Widget _buildTimerAndProgressWidget() {
   Color timerColor = _remainingTime > 300 ? Color(0xFF2E86AB) : Colors.red;
   return Container(
     padding: EdgeInsets.all(16),
     decoration: BoxDecoration(
       color: Colors.white,
       borderRadius: BorderRadius.circular(16),
       boxShadow: [
         BoxShadow(
           color: Colors.black.withValues(alpha: 0.08),
           blurRadius: 12,
           offset: Offset(0, 6),
         ),
       ],
     ),
     child: Directionality(
       textDirection: TextDirection.rtl,
       child: Column(
         children: [
           Row(
             mainAxisAlignment: MainAxisAlignment.spaceBetween,
             children: [
               // عداد الأسئلة (على اليمين)
               Column(
                 crossAxisAlignment: CrossAxisAlignment.start,
                 children: [
                   Text(
                     'السؤال',
                     style: TextStyle(
                       fontFamily: 'Cairo',
                       fontSize: 12,
                       color: Colors.grey.shade600,
                     ),
                   ),
                   SizedBox(height: 2),
                   Text(
                     '${_currentQuestionIndex + 1} / ${_questions.length}',
                     style: TextStyle(
                       fontFamily: 'Cairo',
                       fontSize: 18,
                       fontWeight: FontWeight.bold,
                       color: Color(0xFF2E86AB),
                     ),
                   ),
                 ],
               ),
               // الوقت المتبقي (على اليسار)
               Column(
                 crossAxisAlignment: CrossAxisAlignment.end,
                 children: [
                   Text(
                     'الوقت المتبقي',
                     style: TextStyle(
                       fontFamily: 'Cairo',
                       fontSize: 12,
                       color: Colors.grey.shade600,
                     ),
                   ),
                   SizedBox(height: 2),
                   Row(
                     mainAxisSize: MainAxisSize.min,
                     children: [
                       Text(
                         '${(_remainingTime ~/ 60).toString().padLeft(2, '0')}:${(_remainingTime % 60).toString().padLeft(2, '0')}',
                         style: TextStyle(
                           fontFamily: 'Cairo',
                           fontSize: 18,
                           fontWeight: FontWeight.bold,
                           color: timerColor,
                         ),
                       ),
                       SizedBox(width: 4),
                       Icon(CupertinoIcons.clock, color: timerColor, size: 16),
                     ],
                   ),
                 ],
               ),
             ],
           ),
           SizedBox(height: 12),
           // شريط التقدم
           LinearProgressIndicator(
             value: (_currentQuestionIndex + 1) / _questions.length,
             backgroundColor: Color(0xFFE8F2F7),
             valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E86AB)),
             minHeight: 6,
           ),
         ],
       ),
     ),
   );
 }

 Widget _buildQuestionCard(double screenHeight) {
   return Container(
     padding: EdgeInsets.all(24),
     decoration: BoxDecoration(
       color: Colors.white,
       borderRadius: BorderRadius.circular(20),
       boxShadow: [
         BoxShadow(
           color: Colors.black.withValues(alpha: 0.1),
           blurRadius: 20,
           offset: Offset(0, 10),
         ),
       ],
     ),
     child: Text(
       _questions[_currentQuestionIndex]['question'],
       style: TextStyle(
         fontFamily: 'Cairo',
         fontSize: 18,
         fontWeight: FontWeight.bold,
         color: Color(0xFF2C3E50),
         height: 1.5,
       ),
       textAlign: TextAlign.center,
       textDirection: TextDirection.rtl,
     ),
   );
 }

 List<Widget> _buildAnswerButtons(double screenWidth, bool isSmallScreen) {
   return (_questions[_currentQuestionIndex]['answers'] as List<dynamic>)
       .asMap()
       .entries
       .map((entry) {
     bool isSelected = _userAnswers[_currentQuestionIndex] == entry.key;
     return Padding(
       padding: EdgeInsets.symmetric(vertical: 6.0),
       child: Container(
         decoration: BoxDecoration(
           borderRadius: BorderRadius.circular(15),
           boxShadow: [
             BoxShadow(
               color: Colors.black.withValues(alpha: 0.1),
               blurRadius: 10,
               offset: Offset(0, 5),
             ),
           ],
         ),
         child: ElevatedButton(
           style: ElevatedButton.styleFrom(
             foregroundColor: isSelected ? Colors.white : Color(0xFF2C3E50),
             backgroundColor: isSelected ? Color(0xFF2E86AB) : Colors.white,
             padding: EdgeInsets.symmetric(vertical: 18, horizontal: 20),
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(15),
               side: BorderSide(
                 color: isSelected ? Color(0xFF2E86AB) : Color(0xFFE8F2F7),
                 width: 2,
               ),
             ),
             elevation: 0,
           ),
           onPressed: () => _answerQuestion(entry.key),
           child: Directionality(
             textDirection: TextDirection.rtl,
             child: Row(
               children: [
                 Container(
                   width: 32,
                   height: 32,
                   decoration: BoxDecoration(
                     color: isSelected ? Colors.white : Color(0xFFE8F2F7),
                     borderRadius: BorderRadius.circular(16),
                   ),
                   child: Center(
                     child: Text(
                       String.fromCharCode(65 + entry.key),
                       style: TextStyle(
                         fontFamily: 'Cairo',
                         fontSize: 15,
                         fontWeight: FontWeight.bold,
                         color: isSelected ? Color(0xFF2E86AB) : Color(0xFF2C3E50),
                       ),
                     ),
                   ),
                 ),
                 SizedBox(width: 14),
                 Expanded(
                   child: Text(
                     entry.value,
                     style: TextStyle(
                       fontFamily: 'Cairo',
                       fontSize: isSmallScreen ? 16 : 17,
                       fontWeight: FontWeight.w600,
                       height: 1.4,
                     ),
                     textAlign: TextAlign.right,
                   ),
                 ),
               ],
             ),
           ),
         ),
       ),
     );
   }).toList();
 }

 Widget _buildNavigationButtons(double screenWidth) {
   return Row(
     mainAxisAlignment: MainAxisAlignment.spaceBetween,
     children: [
       SizedBox(
         width: screenWidth * 0.42,
         height: 45,
         child: ElevatedButton(
           onPressed: _currentQuestionIndex < _questions.length - 1
               ? _goToNextQuestion
               : () {
                   _calculateScore();
                   if (_mounted) {
                     setState(() => _showResult = true);
                   }
                 },
           style: ElevatedButton.styleFrom(
             backgroundColor: Color(0xFF2E86AB),
             foregroundColor: Colors.white,
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(12),
             ),
             elevation: 3,
           ),
           child: Text(
             _currentQuestionIndex < _questions.length - 1
                 ? 'السؤال التالي'
                 : 'إنهاء الامتحان',
             style: TextStyle(
               fontFamily: 'Cairo',
               fontSize: 14,
               fontWeight: FontWeight.bold,
             ),
           ),
         ),
       ),
       SizedBox(
         width: screenWidth * 0.42,
         height: 45,
         child: ElevatedButton(
           onPressed: _currentQuestionIndex > 0 ? _goToPreviousQuestion : null,
           style: ElevatedButton.styleFrom(
             backgroundColor: _currentQuestionIndex > 0 ? Colors.white : Colors.grey.shade200,
             foregroundColor: _currentQuestionIndex > 0 ? Color(0xFF2E86AB) : Colors.grey.shade500,
             shape: RoundedRectangleBorder(
               borderRadius: BorderRadius.circular(12),
               side: BorderSide(
                 color: _currentQuestionIndex > 0 ? Color(0xFF2E86AB) : Colors.grey.shade300,
                 width: 1.5,
               ),
             ),
             elevation: 1,
           ),
           child: Text(
             'السؤال السابق',
             style: TextStyle(
               fontFamily: 'Cairo',
               fontSize: 14,
               fontWeight: FontWeight.bold,
             ),
           ),
         ),
       ),
     ],
   );
 }

 Widget _buildResultScreen() {
   return Center(
     child: Container(
       margin: EdgeInsets.all(20),
       padding: EdgeInsets.all(30),
       decoration: BoxDecoration(
         color: Colors.white,
         borderRadius: BorderRadius.circular(25),
         boxShadow: [
           BoxShadow(
             color: Colors.black.withValues(alpha: 0.1),
             blurRadius: 30,
             offset: Offset(0, 15),
           ),
         ],
       ),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         children: [
           Container(
             padding: EdgeInsets.all(20),
             decoration: BoxDecoration(
               color: Color(0xFF2E86AB).withValues(alpha: 0.1),
               borderRadius: BorderRadius.circular(50),
             ),
             child: Icon(
               CupertinoIcons.checkmark_seal,
               size: 50,
               color: Color(0xFF2E86AB),
             ),
           ),
           SizedBox(height: 20),
           Text(
             'نتيجة الامتحان',
             style: TextStyle(
               fontFamily: 'Cairo',
               fontSize: 24,
               fontWeight: FontWeight.bold,
               color: Color(0xFF2E86AB),
             ),
           ),
           SizedBox(height: 15),
           Container(
             padding: EdgeInsets.all(16),
             decoration: BoxDecoration(
               color: Color(0xFFE8F2F7),
               borderRadius: BorderRadius.circular(15),
             ),
             child: Column(
               children: [
                 Text(
                   'لقد أجبت على $_score من أصل ${_questions.length} سؤال بشكل صحيح',
                   style: TextStyle(
                     fontFamily: 'Cairo',
                     fontSize: 16,
                     color: Color(0xFF2C3E50),
                   ),
                   textAlign: TextAlign.center,
                 ),
                 SizedBox(height: 15),
                 Text(
                   'النتيجة النهائية',
                   style: TextStyle(
                     fontFamily: 'Cairo',
                     fontSize: 18,
                     fontWeight: FontWeight.bold,
                     color: Color(0xFF2E86AB),
                   ),
                 ),
                 SizedBox(height: 8),
                 Text(
                   'لقد حصلت على ${_finalScore.toStringAsFixed(1)} من 100',
                   style: TextStyle(
                     fontFamily: 'Cairo',
                     fontSize: 18,
                     fontWeight: FontWeight.bold,
                     color: _finalScore >= 70 ? Colors.green : Colors.red,
                   ),
                 ),
               ],
             ),
           ),
           SizedBox(height: 25),
           Row(
             children: [
               Expanded(
                 child: ElevatedButton(
                   onPressed: _resetQuiz,
                   style: ElevatedButton.styleFrom(
                     foregroundColor: Colors.white,
                     backgroundColor: Color(0xFF2E86AB),
                     padding: EdgeInsets.symmetric(vertical: 16),
                     shape: RoundedRectangleBorder(
                       borderRadius: BorderRadius.circular(15),
                     ),
                     elevation: 5,
                   ),
                   child: Row(
                     mainAxisAlignment: MainAxisAlignment.center,
                     children: [
                       Icon(CupertinoIcons.refresh, size: 18),
                       SizedBox(width: 8),
                       Text(
                         'إعادة الامتحان',
                         style: TextStyle(fontFamily: 'Cairo', fontSize: 16),
                       ),
                     ],
                   ),
                 ),
               ),
               SizedBox(width: 12),
               Expanded(
                 child: ElevatedButton(
                   onPressed: () => _showWrongAnswers(),
                   style: ElevatedButton.styleFrom(
                     foregroundColor: Colors.white,
                     backgroundColor: Colors.red.shade600,
                     padding: EdgeInsets.symmetric(vertical: 16),
                     shape: RoundedRectangleBorder(
                       borderRadius: BorderRadius.circular(15),
                     ),
                     elevation: 5,
                   ),
                   child: Row(
                     mainAxisAlignment: MainAxisAlignment.center,
                     children: [
                       Icon(CupertinoIcons.xmark_circle, size: 18),
                       SizedBox(width: 8),
                       Text(
                         'مراجعة الأخطاء',
                         style: TextStyle(fontFamily: 'Cairo', fontSize: 14),
                       ),
                     ],
                   ),
                 ),
               ),
             ],
           ),
         ],
       ),
     ),
   );
 }

 void _showWrongAnswers() {
   List<int> wrongAnswers = [];
   for (int i = 0; i < _questions.length; i++) {
     if (_userAnswers[i] != _questions[i]['correctAnswer']) {
       wrongAnswers.add(i);
     }
   }

   showDialog(
     context: context,
     builder: (BuildContext context) {
       return Dialog(
         shape: RoundedRectangleBorder(
           borderRadius: BorderRadius.circular(20),
         ),
         child: Container(
           width: double.maxFinite,
           height: MediaQuery.of(context).size.height * 0.8,
           decoration: BoxDecoration(
             borderRadius: BorderRadius.circular(20),
             color: Colors.white,
           ),
           child: ClipRRect(
             borderRadius: BorderRadius.circular(20),
             child: Column(
               children: [
                 // Header
                 Container(
                   padding: EdgeInsets.all(24),
                   decoration: BoxDecoration(
                     gradient: LinearGradient(
                       colors: [Colors.red.shade400, Colors.red.shade600],
                     ),
                   ),
                   child: Row(
                     children: [
                       Container(
                         padding: EdgeInsets.all(8),
                         decoration: BoxDecoration(
                           color: Colors.white.withValues(alpha: 0.2),
                           borderRadius: BorderRadius.circular(12),
                         ),
                         child: Icon(
                           CupertinoIcons.exclamationmark_triangle_fill,
                           color: Colors.white,
                           size: 28,
                         ),
                       ),
                       SizedBox(width: 12),
                       Expanded(
                         child: Column(
                           crossAxisAlignment: CrossAxisAlignment.start,
                           children: [
                             Text(
                               'مراجعة الأخطاء',
                               style: TextStyle(
                                 fontFamily: 'Cairo',
                                 fontSize: 20,
                                 fontWeight: FontWeight.bold,
                                 color: Colors.white,
                               ),
                             ),
                             Text(
                               wrongAnswers.isEmpty
                                 ? 'لا توجد أخطاء - أداء ممتاز!'
                                 : wrongAnswers.length == 1
                                   ? 'خطأ واحد من أصل ${_questions.length} سؤال'
                                   : wrongAnswers.length == 2
                                     ? 'خطأن من أصل ${_questions.length} سؤال'
                                     : wrongAnswers.length >= 3 && wrongAnswers.length <= 10
                                       ? '${wrongAnswers.length} أخطاء من أصل ${_questions.length} سؤال'
                                       : '${wrongAnswers.length} خطأ من أصل ${_questions.length} سؤال',
                               style: TextStyle(
                                 fontFamily: 'Cairo',
                                 fontSize: 14,
                                 color: Colors.white.withValues(alpha: 0.9),
                               ),
                               textAlign: TextAlign.right,
                               textDirection: TextDirection.rtl,
                             ),
                           ],
                         ),
                       ),
                       IconButton(
                         onPressed: () => Navigator.pop(context),
                         icon: Icon(
                           CupertinoIcons.xmark,
                           color: Colors.white,
                           size: 24,
                         ),
                       ),
                     ],
                   ),
                 ),

                 // Content
                 Expanded(
                   child: wrongAnswers.isEmpty
                       ? Center(
                           child: Column(
                             mainAxisAlignment: MainAxisAlignment.center,
                             children: [
                               Container(
                                 padding: EdgeInsets.all(20),
                                 decoration: BoxDecoration(
                                   color: Colors.green.shade100,
                                   borderRadius: BorderRadius.circular(50),
                                 ),
                                 child: Icon(
                                   CupertinoIcons.checkmark_seal_fill,
                                   size: 60,
                                   color: Colors.green.shade600,
                                 ),
                               ),
                               SizedBox(height: 20),
                               Text(
                                 'ممتاز! 🎉',
                                 style: TextStyle(
                                   fontFamily: 'Cairo',
                                   fontSize: 24,
                                   fontWeight: FontWeight.bold,
                                   color: Colors.green.shade700,
                                 ),
                               ),
                               SizedBox(height: 8),
                               Text(
                                 'لقد أجبت على جميع الأسئلة بشكل صحيح\nلا توجد أخطاء تحتاج إلى مراجعة',
                                 style: TextStyle(
                                   fontFamily: 'Cairo',
                                   fontSize: 16,
                                   color: Colors.green.shade600,
                                   height: 1.5,
                                 ),
                                 textAlign: TextAlign.center,
                                 textDirection: TextDirection.rtl,
                               ),
                               SizedBox(height: 40),
                               // Close button for perfect score
                               Container(
                                 margin: EdgeInsets.symmetric(horizontal: 40),
                                 decoration: BoxDecoration(
                                   borderRadius: BorderRadius.circular(15),
                                   boxShadow: [
                                     BoxShadow(
                                       color: Color(0xFF2E86AB).withValues(alpha: 0.3),
                                       blurRadius: 10,
                                       offset: Offset(0, 5),
                                     ),
                                   ],
                                 ),
                                 child: SizedBox(
                                   width: double.infinity,
                                   height: 50,
                                   child: ElevatedButton(
                                     onPressed: () => Navigator.pop(context),
                                     style: ElevatedButton.styleFrom(
                                       backgroundColor: Color(0xFF2E86AB),
                                       foregroundColor: Colors.white,
                                       shape: RoundedRectangleBorder(
                                         borderRadius: BorderRadius.circular(15),
                                       ),
                                       elevation: 0,
                                     ),
                                     child: Row(
                                       mainAxisAlignment: MainAxisAlignment.center,
                                       children: [
                                         Icon(
                                           CupertinoIcons.xmark_circle,
                                           size: 20,
                                         ),
                                         SizedBox(width: 8),
                                         Text(
                                           'إغلاق',
                                           style: TextStyle(
                                             fontFamily: 'Cairo',
                                             fontSize: 16,
                                             fontWeight: FontWeight.bold,
                                           ),
                                         ),
                                       ],
                                     ),
                                   ),
                                 ),
                               ),
                             ],
                           ),
                         )
                       : Container(
                           color: Color(0xFFFAFAFA),
                           child: ListView.builder(
                             padding: EdgeInsets.all(16),
                             itemCount: wrongAnswers.length + 2, // +1 for info card, +1 for close button
                             itemBuilder: (context, index) {
                               // Info card at the beginning
                               if (index == 0) {
                                 return Container(
                                   margin: EdgeInsets.only(bottom: 20),
                                   padding: EdgeInsets.all(20),
                                   decoration: BoxDecoration(
                                     color: Colors.blue.shade50,
                                     borderRadius: BorderRadius.circular(16),
                                     border: Border.all(
                                       color: Colors.blue.shade200,
                                       width: 1,
                                     ),
                                   ),
                                   child: Column(
                                     children: [
                                       Row(
                                         children: [
                                           Container(
                                             padding: EdgeInsets.all(8),
                                             decoration: BoxDecoration(
                                               color: Colors.blue.shade600,
                                               borderRadius: BorderRadius.circular(20),
                                             ),
                                             child: Icon(
                                               CupertinoIcons.info,
                                               color: Colors.white,
                                               size: 16,
                                             ),
                                           ),
                                           SizedBox(width: 12),
                                           Expanded(
                                             child: Text(
                                               'مراجعة الإجابات الخاطئة',
                                               style: TextStyle(
                                                 fontFamily: 'Cairo',
                                                 fontSize: 16,
                                                 color: Colors.blue.shade700,
                                                 fontWeight: FontWeight.bold,
                                               ),
                                             ),
                                           ),
                                         ],
                                       ),
                                       SizedBox(height: 12),
                                       Text(
                                         'هذه هي الأسئلة التي أخطأت في الإجابة عنها، إلى جانب الإجابات الصحيحة. خصص بعض الوقت لمراجعتها بدقة لتقوية معلوماتك وزيادة فرصك في تحقيق نتائج أفضل في الاختبار.',
                                         style: TextStyle(
                                           fontFamily: 'Cairo',
                                           fontSize: 14,
                                           color: Colors.blue.shade600,
                                           height: 1.5,
                                         ),
                                         textAlign: TextAlign.right,
                                         textDirection: TextDirection.rtl,
                                       ),
                                     ],
                                   ),
                                 );
                               }

                               // Close button at the end
                               if (index == wrongAnswers.length + 1) {
                                 return Container(
                                   margin: EdgeInsets.only(top: 20, bottom: 40),
                                   padding: EdgeInsets.symmetric(horizontal: 20),
                                   child: Container(
                                     decoration: BoxDecoration(
                                       borderRadius: BorderRadius.circular(15),
                                       boxShadow: [
                                         BoxShadow(
                                           color: Color(0xFF2E86AB).withValues(alpha: 0.3),
                                           blurRadius: 10,
                                           offset: Offset(0, 5),
                                         ),
                                       ],
                                     ),
                                     child: SizedBox(
                                       width: double.infinity,
                                       height: 50,
                                       child: ElevatedButton(
                                         onPressed: () => Navigator.pop(context),
                                         style: ElevatedButton.styleFrom(
                                           backgroundColor: Color(0xFF2E86AB),
                                           foregroundColor: Colors.white,
                                           shape: RoundedRectangleBorder(
                                             borderRadius: BorderRadius.circular(15),
                                           ),
                                           elevation: 0,
                                         ),
                                         child: Row(
                                           mainAxisAlignment: MainAxisAlignment.center,
                                           children: [
                                             Icon(
                                               CupertinoIcons.xmark_circle,
                                               size: 20,
                                             ),
                                             SizedBox(width: 8),
                                             Text(
                                               'إغلاق',
                                               style: TextStyle(
                                                 fontFamily: 'Cairo',
                                                 fontSize: 16,
                                                 fontWeight: FontWeight.bold,
                                               ),
                                             ),
                                           ],
                                         ),
                                       ),
                                     ),
                                   ),
                                 );
                               }

                               int questionIndex = wrongAnswers[index - 1]; // -1 because of info card
                               return Container(
                                 margin: EdgeInsets.only(bottom: 20),
                                 decoration: BoxDecoration(
                                   color: Colors.white,
                                   borderRadius: BorderRadius.circular(16),
                                   border: Border.all(
                                     color: Colors.red.shade200,
                                     width: 2,
                                   ),
                                   boxShadow: [
                                     BoxShadow(
                                       color: Colors.red.withValues(alpha: 0.1),
                                       blurRadius: 8,
                                       offset: Offset(0, 4),
                                     ),
                                   ],
                                 ),
                                 child: Column(
                                   crossAxisAlignment: CrossAxisAlignment.start,
                                   children: [
                                     // Question Header
                                     Container(
                                       padding: EdgeInsets.all(16),
                                       decoration: BoxDecoration(
                                         color: Colors.red.shade600,
                                         borderRadius: BorderRadius.only(
                                           topLeft: Radius.circular(14),
                                           topRight: Radius.circular(14),
                                         ),
                                       ),
                                       child: Row(
                                         children: [
                                           Container(
                                             padding: EdgeInsets.symmetric(
                                               horizontal: 12,
                                               vertical: 6,
                                             ),
                                             decoration: BoxDecoration(
                                               color: Colors.white,
                                               borderRadius: BorderRadius.circular(12),
                                             ),
                                             child: Text(
                                               'سؤال ${questionIndex + 1}',
                                               style: TextStyle(
                                                 fontFamily: 'Cairo',
                                                 fontSize: 12,
                                                 fontWeight: FontWeight.bold,
                                                 color: Colors.red.shade600,
                                               ),
                                             ),
                                           ),
                                           Spacer(),
                                           Icon(
                                             CupertinoIcons.xmark_circle_fill,
                                             color: Colors.white,
                                             size: 24,
                                           ),
                                         ],
                                       ),
                                     ),

                                     // Question Text
                                     Container(
                                       padding: EdgeInsets.all(20),
                                       decoration: BoxDecoration(
                                         color: Colors.grey.shade50,
                                       ),
                                       child: Text(
                                         _questions[questionIndex]['question'],
                                         style: TextStyle(
                                           fontFamily: 'Cairo',
                                           fontSize: 16,
                                           fontWeight: FontWeight.bold,
                                           color: Color(0xFF2C3E50),
                                           height: 1.5,
                                         ),
                                         textAlign: TextAlign.right,
                                         textDirection: TextDirection.rtl,
                                       ),
                                     ),

                                     // Answers Section
                                     Container(
                                       padding: EdgeInsets.all(16),
                                       child: Column(
                                         children: [
                                           // Wrong Answer
                                           if (_userAnswers[questionIndex] != null)
                                             Container(
                                               width: double.infinity,
                                               padding: EdgeInsets.all(16),
                                               decoration: BoxDecoration(
                                                 color: Colors.red.shade50,
                                                 borderRadius: BorderRadius.circular(12),
                                                 border: Border.all(
                                                   color: Colors.red.shade300,
                                                   width: 2,
                                                 ),
                                               ),
                                               child: Column(
                                                 crossAxisAlignment: CrossAxisAlignment.start,
                                                 children: [
                                                   Row(
                                                     children: [
                                                       Container(
                                                         padding: EdgeInsets.all(8),
                                                         decoration: BoxDecoration(
                                                           color: Colors.red.shade600,
                                                           borderRadius: BorderRadius.circular(20),
                                                         ),
                                                         child: Icon(
                                                           CupertinoIcons.xmark,
                                                           color: Colors.white,
                                                           size: 16,
                                                         ),
                                                       ),
                                                       SizedBox(width: 12),
                                                       Text(
                                                         'اختيارك (خطأ)',
                                                         style: TextStyle(
                                                           fontFamily: 'Cairo',
                                                           fontSize: 14,
                                                           color: Colors.red.shade700,
                                                           fontWeight: FontWeight.bold,
                                                         ),
                                                       ),
                                                     ],
                                                   ),
                                                   SizedBox(height: 12),
                                                   Container(
                                                     width: double.infinity,
                                                     padding: EdgeInsets.all(12),
                                                     decoration: BoxDecoration(
                                                       color: Colors.white,
                                                       borderRadius: BorderRadius.circular(8),
                                                       border: Border.all(
                                                         color: Colors.red.shade200,
                                                         width: 1,
                                                       ),
                                                     ),
                                                     child: Text(
                                                       _questions[questionIndex]['answers'][_userAnswers[questionIndex]],
                                                       style: TextStyle(
                                                         fontFamily: 'Cairo',
                                                         fontSize: 15,
                                                         color: Colors.red.shade800,
                                                         fontWeight: FontWeight.w600,
                                                       ),
                                                       textAlign: TextAlign.right,
                                                       textDirection: TextDirection.rtl,
                                                     ),
                                                   ),
                                                 ],
                                               ),
                                             ),

                                           if (_userAnswers[questionIndex] != null)
                                             SizedBox(height: 16),

                                           // Correct Answer
                                           Container(
                                             width: double.infinity,
                                             padding: EdgeInsets.all(16),
                                             decoration: BoxDecoration(
                                               color: Colors.green.shade50,
                                               borderRadius: BorderRadius.circular(12),
                                               border: Border.all(
                                                 color: Colors.green.shade300,
                                                 width: 2,
                                               ),
                                             ),
                                             child: Column(
                                               crossAxisAlignment: CrossAxisAlignment.start,
                                               children: [
                                                 Row(
                                                   children: [
                                                     Container(
                                                       padding: EdgeInsets.all(8),
                                                       decoration: BoxDecoration(
                                                         color: Colors.green.shade600,
                                                         borderRadius: BorderRadius.circular(20),
                                                       ),
                                                       child: Icon(
                                                         CupertinoIcons.checkmark,
                                                         color: Colors.white,
                                                         size: 16,
                                                       ),
                                                     ),
                                                     SizedBox(width: 12),
                                                     Text(
                                                       'الإجابة الصحيحة هي',
                                                       style: TextStyle(
                                                         fontFamily: 'Cairo',
                                                         fontSize: 14,
                                                         color: Colors.green.shade700,
                                                         fontWeight: FontWeight.bold,
                                                       ),
                                                     ),
                                                   ],
                                                 ),
                                                 SizedBox(height: 12),
                                                 Container(
                                                   width: double.infinity,
                                                   padding: EdgeInsets.all(12),
                                                   decoration: BoxDecoration(
                                                     color: Colors.white,
                                                     borderRadius: BorderRadius.circular(8),
                                                     border: Border.all(
                                                       color: Colors.green.shade200,
                                                       width: 1,
                                                     ),
                                                   ),
                                                   child: Text(
                                                     _questions[questionIndex]['answers'][_questions[questionIndex]['correctAnswer']],
                                                     style: TextStyle(
                                                       fontFamily: 'Cairo',
                                                       fontSize: 15,
                                                       color: Colors.green.shade800,
                                                       fontWeight: FontWeight.bold,
                                                     ),
                                                     textAlign: TextAlign.right,
                                                     textDirection: TextDirection.rtl,
                                                   ),
                                                 ),
                                               ],
                                             ),
                                           ),
                                         ],
                                       ),
                                     ),
                                   ],
                                 ),
                               );
                             },
                           ),
                         ),
                 ),
               ],
             ),
           ),
         ),
       );
     },
   );
 }


}
